-- // create_tb_user_alarms
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_user_alarms` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT  '主键ID',
  `tal_id` varchar(256) NOT NULL   DEFAULT '' COMMENT 'tal_id',
  `union_id` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '唯一UUID',
  `client_id` INT(11)  NOT NULL  DEFAULT 0 COMMENT '设备端维护的此数据ID值',
  `alarm_name` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '闹钟名称',
  `alarm_time` INT(11)  NOT NULL  DEFAULT '0' COMMENT '闹钟时间（24小时制 由HH:MM 转为秒级时间）',
  `repeat_days` varchar(256) NOT NULL  DEFAULT '' COMMENT '重复日（1-7，周一到周日）格式: 1,4,5',
  `repeating` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否重复，0-否，1-是',
  `alarm_day` varchar(256) NOT NULL DEFAULT '' COMMENT '响铃具体日期，仅一次性闹钟需要',
  `icon_id` VARCHAR(256)  NOT NULL DEFAULT '' COMMENT 'icon ID',
  `ring_id` VARCHAR(256)  NOT NULL DEFAULT '' COMMENT '铃声ID',
  `enabled` INT(2)  NOT NULL DEFAULT 1 COMMENT '是否启用，0-否，1-是',
  `deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
  `is_pre_set` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否预置闹钟 0-否，1-是',
  `pre_set_type` INT(2)  NOT NULL DEFAULT 0 COMMENT '预置闹钟类型 0-其他 1-wakeUp 2-sleep',
  `last_modified_by` INT(2)  NOT NULL DEFAULT 1 COMMENT '最后修改者，1-device，2-parent',
  `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号，服务端内部自增',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_talid_union_id` (`tal_id`,`union_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_alarm_time` (`alarm_time`),
  KEY `idx_update_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户闹钟表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_user_alarms;

