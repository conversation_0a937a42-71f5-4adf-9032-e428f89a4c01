-- // create_tb_user_pet_house_parts
-- Migration SQL that makes the change goes here.

CREATE TABLE `tb_user_pet_house_parts` (
                                           `id` bigint NOT NULL COMMENT '主键ID',
                                           `tal_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户ID',
                                           `pet_id` int NOT NULL DEFAULT 0 COMMENT '宠物ID',
                                           `pbg_id` varchar(64) NOT NULL DEFAULT '' COMMENT '背景id编号',
                                           `select_prop` text NOT NULL COMMENT 'JSON格式保存的装扮组件',
                                           `del_flag` int(2) NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_tal_id` (`tal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户宠物小屋装扮表';


-- //@UNDO
-- SQL to undo the change goes here.

drop table tb_user_pet_house_parts
