-- // create_tb_user_pets
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_user_pets` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tal_id` varchar(256) NOT NULL   DEFAULT '' COMMENT 'tal_id',
    `union_id` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '唯一UUID',
    `client_id` INT(11)  NOT NULL  DEFAULT 0 COMMENT '设备端维护的此数据ID值',
    `pet_id` INT(11)  NOT NULL  DEFAULT '0' COMMENT '宠物ID',
    `pet_name` varchar(256) NOT NULL   DEFAULT '' COMMENT '宠物名称',
    `pet_level` TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '宠物等级，1-4',
    `selected` INT(2) NOT NULL  DEFAULT 0 COMMENT '0 未选中 1选中',
    `hunger` DECIMAL(16,8) NOT NULL  DEFAULT 0 COMMENT '饥饿度',
    `unhappy` DECIMAL(16,8) NOT NULL  DEFAULT 0 COMMENT '不开心度',
    `dirt` DECIMAL(16,8) NOT NULL  DEFAULT 0 COMMENT '不洁净',
    `rp` DECIMAL(16,8) NOT NULL  DEFAULT 0 COMMENT '交互值',
    `wakeup_alarm_count`  INT(11) NOT NULL  DEFAULT 0 COMMENT '起床闹钟',
    `sleep_alarm_count`  INT(11) NOT NULL  DEFAULT 0 COMMENT '睡觉闹钟',
    `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号，服务端内部自增',
    `deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
    `last_modified_by` INT(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最后修改者，1-device，2-parent',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_union_id` (`union_id`),
    UNIQUE KEY `idx_talid_pet_id` (`tal_id`,`pet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户宠物表';

-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_user_pets;

