-- // create_tb_pet_album_background
-- Migration SQL that makes the change goes here.

CREATE TABLE `tb_pet_album_background` (
                                     `id` bigint NOT NULL COMMENT '主键ID',
                                     `pet_id` int NOT NULL DEFAULT 0 COMMENT '宠物ID',
                                     `pbg_id` varchar(64) NOT NULL DEFAULT '' COMMENT '背景id编号',
                                     `level` int NOT NULL DEFAULT 0 COMMENT '解锁等级',
                                     `thumbnail_url` varchar(255) NOT NULL DEFAULT '' COMMENT '缩略图资源地址',
                                     `full_image_url` varchar(255) NOT NULL DEFAULT '' COMMENT '大图资源地址',
                                     `sort` int NOT NULL DEFAULT 0 COMMENT '排列顺序',
                                     `app_version` bigint NOT NULL DEFAULT 0 COMMENT 'App版本号',
                                     `del_flag` int(2) NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_pbg_id` (`pbg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宠物相册背景表';


-- //@UNDO
-- SQL to undo the change goes here.

drop table tb_pet_album_prop;
