-- // create_table_tb_audio
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_audio` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`union_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '唯一标识',
`scene_mode` INT(2) NOT NULL DEFAULT 1 COMMENT '情景模式，1-日间模式，2-哄睡模式',
`content_type` INT(2) NOT NULL DEFAULT 1 COMMENT '音频内容类型，1-故事，2-音乐，3-播客',
`name` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '音频名称',
`subject_desc` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '主题描述',
`cover_url` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '音频封面地址',
`audio_url` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '音频地址',
`sequence` INT(11) NOT NULL DEFAULT 0 COMMENT '播放顺序',
`deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_scene_mode_sequence` (`scene_mode`,`sequence`),
KEY `idx_union_id` (`union_id`),
KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_audio;

