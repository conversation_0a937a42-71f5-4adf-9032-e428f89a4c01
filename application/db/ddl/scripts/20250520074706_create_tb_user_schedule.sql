-- // create_tb_user_schedule
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_user_schedule` (
    `id` bigint NOT NULL AUTO_INCREMENT  PRIMARY  KEY COMMENT '主键ID',
    `tal_id` varchar(256) NOT NULL   DEFAULT '' COMMENT 'tal_id',
    `union_id` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '唯一UUID',
    `client_id` INT(11)  NOT NULL  DEFAULT 0 COMMENT '设备端维护的此数据ID值',
    `name` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '日程名称',
    `schedule_time` INT(11)  NOT NULL  DEFAULT '0' COMMENT '日程时间 24小时制的秒数',
    `notify_duration` INT(5)  NOT NULL  DEFAULT 5 COMMENT '提醒时长 1,5,10,20,30 单位分钟',
    `repeat_days` varchar(256) NOT NULL  DEFAULT '' COMMENT '重复日 1-7',
    `repeating` INT(2) NOT NULL DEFAULT 0 COMMENT '是否重复，0-否，1-是',
    `schedule_day` varchar(256) NOT NULL DEFAULT '' COMMENT '日程具体日期，仅一次性日程需要',
    `icon_id` VARCHAR(256)  NOT NULL DEFAULT '' COMMENT 'icon ID',
    `colour` VARCHAR(256)  NOT NULL DEFAULT '' COMMENT 'label颜色',
    `enabled` INT(2)  NOT NULL DEFAULT 1 COMMENT '是否启用，0-否，1-是',
    `deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
    `is_pre_set` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否预置日程 0-否，1-是',
    `pre_set_type` INT(2)  NOT NULL DEFAULT 0 COMMENT '预置日程类型 0-其他 1-Brush Teeth 2-Healthy Snacks 3-Play Outside等',
    `last_modified_by` INT(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最后修改者，1-device，2-parent',
    `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号，服务端内部自增',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    UNIQUE KEY `idx_talid_union_id` (`tal_id`,`union_id`),
    KEY `idx_union_id` (`union_id`),
    KEY `idx_schedule_time`(schedule_time),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户日程表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_user_schedule;

