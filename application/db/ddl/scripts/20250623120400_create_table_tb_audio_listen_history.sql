-- // create_table_tb_audio_listen_history
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_audio_listen_history` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`tal_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'tal_id',
`audio_union_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '音频唯一标识',
`scene_mode` INT(2) NOT NULL DEFAULT 1 COMMENT '情景模式，1-日间模式，2-哄睡模式',
`listen_time` bigint NOT NULL DEFAULT 0 COMMENT '收听时间（时间戳，毫秒）',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_tal_id_scene_mode` (`tal_id`,`scene_mode`),
KEY `idx_audio_union_id` (`audio_union_id`),
KEY `idx_listen_time` (`listen_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频收听历史表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_audio_listen_history;

