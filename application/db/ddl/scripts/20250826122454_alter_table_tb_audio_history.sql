-- // alter table tb_audio_history
-- Migration SQL that makes the change goes here.
alter table tb_audio_listen_history
    add album_id bigint default 0 not null comment '专辑id' after scene_mode;

alter table tb_audio_listen_history
    add content_type smallint default 0 not null comment '音频内容类型，1-故事，2-音乐，3-播客' after album_id;

alter table tb_audio_listen_history
    add age_range_id smallint default 0 not null comment '年龄段id' after content_type;

alter table tb_audio_listen_history
    add type smallint default 1 not null comment '类型（1播放列表、2专辑）' after age_range_id;

alter table tb_audio_listen_history
    add duration bigint default 0 not null comment '播放时长' after scene_mode;



-- //@UNDO
-- SQL to undo the change goes here.


