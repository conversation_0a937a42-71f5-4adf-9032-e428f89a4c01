-- // create table tb_audio_album
-- Migration SQL that makes the change goes here.
CREATE TABLE tb_audio_album
(
    id           BIGINT(10) UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    name         varchar(128) default ''                not null comment '专辑名称',
    cover_url    varchar(255) default ''                not null comment '专辑封面图',
    content_type smallint     default 0                 not null comment '音频内容类型，1-故事，2-音乐，3-播客',
    sort         int          default 0                 not null comment '排序值，值越小越靠前',
    del_flag     tinyint(1)   default 0                 not null comment '是否删除（0-未删除，1-已删除）',
    create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT = '专辑表';



-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_audio_album;

