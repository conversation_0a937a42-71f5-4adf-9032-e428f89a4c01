-- // create_tb_night_light_config
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_night_light_config` (
     `id` bigint NOT NULL AUTO_INCREMENT  COMMENT '主键ID',
     `tal_id` varchar(256) NOT NULL   DEFAULT '' COMMENT 'tal_id',
     `union_id` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '唯一UUID',
     `client_id` INT(11)  NOT NULL  DEFAULT 0 COMMENT '设备端维护的此数据ID值',
     `brightness` INT(3)  NOT NULL  DEFAULT '0' COMMENT '亮度',
     `lighting_effects` INT(2)  NOT NULL  DEFAULT '0' COMMENT '灯效 1常亮 2 闪烁效果 3 呼吸效果',
     `auto_light` INT(2)  NOT NULL DEFAULT 1 COMMENT '自动照明，0-关闭，1-开启',
     `color` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '灯光颜色',
     `last_modified_by` INT(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最后修改者，1-device，2-parent',
     `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号，服务端内部自增',
     `deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `idx_talid_id` (`tal_id`),
     KEY `idx_union_id` (`union_id`),
     KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户夜灯设置';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_night_light_config;

