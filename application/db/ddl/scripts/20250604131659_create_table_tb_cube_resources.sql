-- // create_table_tb_cube_resources
-- Migration SQL that makes the change goes here.
CREATE TABLE `tb_cube_resources` (
 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
 `bus_type` INT(2) NOT NULL   DEFAULT 0 COMMENT '适用于的业务 1闹钟 2日程',
 `type` INT(2)  NOT NULL DEFAULT 0 COMMENT '资源类型 1.icon 2.ring',
 `name` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '文件名',
 `file_id` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '文件唯一标识(设备端和家长端保持一致)',
 `url` VARCHAR(256) NOT NULL  DEFAULT '' COMMENT '文件地址',
 `deleted` INT(2)  NOT NULL DEFAULT 0 COMMENT '是否已删除，0-否，1-是',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
 PRIMARY KEY (`id`),
 UNIQUE KEY `idx_file_id` (`bus_type`,`type`,`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='闹钟资源表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_cube_resources;

