-- // create table tb_album_detail
-- Migration SQL that makes the change goes here.
CREATE TABLE tb_album_detail
(
    id          BIGINT(10) UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    audio_id    varchar(64)  default ''                not null comment '音频id',
    album_id    bigint       default 0                 not null comment '专辑id',
    name        varchar(128) default ''                not null comment '专辑名称',
    sort        int          default 0                 not null comment '排序值，值越小越靠前',
    del_flag    tinyint(1)   default 0                 not null comment '是否删除（0-未删除，1-已删除）',
    create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT = '专辑详情表';


-- //@UNDO
-- SQL to undo the change goes here.
drop table tb_album_detail;

