<?xml version="1.0" encoding="UTF-8" ?>
<!--<Configuration xmlns="http://logging.apache.org/log4j/2.0/config">-->
<Configuration>
    <Properties>
        <Property name="LOG_HOME">/home/<USER>/xeslog</Property>
        <Property name="JSON_PATTERN">{"x_timestamp":"%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}","x_level":"%-5level","x_trace_id":"%X{traceId}","x_rpc_id":"%X{rpcId}","x_sn":"%X{sn}","x_tal_id":"%X{talId}","x_duration":"%X{cost}","x_module":"%c{1}.%M():@%L","x_server_ip":"${env:HOSTNAME}","x_msg":"%enc{%m}{JSON}","x_backtrace":"%enc{%ex{short}}{JSON}"}%n</Property>
    </Properties>
    <Appenders>
        <RollingFile name="SYS_STDOUT" fileName="${LOG_HOME}/stdout.log" filePattern="${LOG_HOME}/stdout.%d{yyyy-MM-dd}.log">
            <Filters>
                <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout>
                <charset>UTF-8</charset>
                <Pattern>${JSON_PATTERN}</Pattern>
                <charset>UTF-8</charset>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>

        <RollingFile name="SYS_ERROR" fileName="${LOG_HOME}/error.log" filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}.log">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout>
                <charset>UTF-8</charset>
                <Pattern>${JSON_PATTERN}</Pattern>
                <charset>UTF-8</charset>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>

    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="SYS_STDOUT"/>
            <AppenderRef ref="SYS_ERROR"/>
        </Root>
    </Loggers>
</Configuration>