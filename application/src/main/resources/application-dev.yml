server:
  port: 9030

management:
  server:
    port: 9030
  metrics:
    tags:
      application: ${spring.application.name}
  endpoints:
    web:
      exposure:
        include: health,prometheus,metrics,loggers,info
  endpoint:
    health:
      show-details: always
logging:
  config: "classpath:log4j2-local.xml"
  level:
    com.zaxxer.hikari.pool.HikariPool: debug

spring:
  application:
    name: cube-703-datahub-application
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  data:
    redis:
      host: 127.0.0.1
      port: 6379
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************
    username: root
    password: root111111
  kafka:
    consumer:
      bootstrap-servers: taloversea-2.servicebus.chinacloudapi.cn:9093
      key-serializer: org.apache.kafka.common.serialization.StringDeserializer
      value-serializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-offset-reset: earliest
      #sasl配置，按需配置
      properties:
        sasl.mechanism: PLAIN
        security.protocol: SASL_SSL
        sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="Endpoint=sb://taloversea-2.servicebus.chinacloudapi.cn/;SharedAccessKeyName=taloversea-app-2;SharedAccessKey=uLEhOJfujiFH2krDdqmr+BObG4LSNENXo+AEhGc/B0A=";
      user-logout-topic:
        topic: family-delete-event-test
        consumer-group: consumer-cube-703-datahub-test

sea:
  alarm:
    xiaotianquantaskid: 1463604
    xiaotianquantoken: f67297c33c6fd8fdef5db89900efd76449ca70f9
#iot服务
iot:
  service:
    url: http://cube-701-devicebase-test-svc/cube-701-devicebase/innerapi/v1/iot/direct-method
#cdn域名前缀
cdn:
    domain: https://static.thinkbuddycdn.com

#内容安全审核等级配置
safety:
  categorySeverityMap:
    Hate: 1
    SelfHarm: 1
    Sexual: 1
    Violence: 1