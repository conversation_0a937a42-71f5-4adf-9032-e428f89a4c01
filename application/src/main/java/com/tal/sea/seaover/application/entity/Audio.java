package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 音频实体
 *
 * @TableName tb_audio
 */
@TableName(value = "tb_audio")
@Data
public class Audio implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识
     */
    private String unionId;

    /**
     * 情景模式，1-日间模式，2-哄睡模式
     */
    private Integer sceneMode;

    /**
     * 音频内容类型，1-故事，2-音乐，3-播客
     */
    private Integer contentType;

    /**
     * 音频名称
     */
    private String name;

    /**
     * 主题描述
     */
    private String subjectDesc;

    /**
     * 音频封面
     */
    private String coverUrl;

    /**
     * 年龄段
     */
    private String ageRangeIds;

    /**
     * 音频地址
     */
    private String audioUrl;

    /**
     * 播放顺序
     */
    private Integer sequence;

    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}