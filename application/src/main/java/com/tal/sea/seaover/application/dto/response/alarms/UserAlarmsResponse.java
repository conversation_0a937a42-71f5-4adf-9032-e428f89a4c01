package com.tal.sea.seaover.application.dto.response.alarms;

import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.ResourceTypeEnum;
import com.tal.sea.seaover.application.enums.ResourcesBusTypeEnum;
import com.tal.sea.seaover.application.service.resources.CubeResourcesService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class UserAlarmsResponse extends UserAlarms {
    /**
     * icon ID
     */
    private String iconUrl;

    /**
     * 铃声ID
     */
    private String ringUrl;

    public void setIconAndRingUrl(CubeResourcesService cubeResourcesService){
        if (StringUtils.isNotEmpty(super.getIconId())){
            this.setIconUrl(cubeResourcesService.getUrlByFileId(ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.ICON,super.getIconId()));
        }
        if (StringUtils.isNotEmpty(super.getRingId())){
            this.setRingUrl(cubeResourcesService.getUrlByFileId(ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.RING,super.getRingId()));
        }
    }
}
