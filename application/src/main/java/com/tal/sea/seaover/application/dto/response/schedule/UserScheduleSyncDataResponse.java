package com.tal.sea.seaover.application.dto.response.schedule;

import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户闹钟表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserScheduleSyncDataResponse extends SyncDataParentResponse implements Serializable {

    /**
     * 日程名称
     */
    private String name;

    /**
     * 日程时间 24小时制的秒数
     */
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    private Integer notifyDuration;

    /**
     * 重复日 1-7
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * label颜色
     */
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;


    /**
     * 是否预置日程 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 预置日程类型
     */
    private Integer preSetType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;


    private static final long serialVersionUID = 1L;
}