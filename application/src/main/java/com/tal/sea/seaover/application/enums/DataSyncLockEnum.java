package com.tal.sea.seaover.application.enums;

import com.tal.sea.seaover.application.constant.RedisConstant;

import java.util.Arrays;

/**
 * 数据同步锁枚举
 */
public enum DataSyncLockEnum {
    ALARM(DataSyncProcessorEnum.ALARM.getType(), RedisConstant.USER_OPERATION_ALARM_LOCK),

    SCHEDULE(DataSyncProcessorEnum.SCHEDULE.getType(), RedisConstant.USER_OPERATION_SCHEDULE_LOCK),

    NIGHT_LIGHT(DataSyncProcessorEnum.NIGHT_LIGHT.getType(), RedisConstant.USER_OPERATION_NIGHT_LIGHT_LOCK),

    PETS(DataSyncProcessorEnum.PETS.getType(), RedisConstant.USER_OPERATION_PETS_LOCK);

    private final String type;
    private final String lockPrefix;

    DataSyncLockEnum(String type, String lockPrefix) {
        this.type = type;
        this.lockPrefix = lockPrefix;
    }

    public String getType() {
        return type;
    }

    public String getLockPrefix() {
        return lockPrefix;
    }

    public static DataSyncLockEnum fromType(DataSyncProcessorEnum processorEnum) {
        return Arrays.stream(DataSyncLockEnum.values())
                .filter(e -> e.getType().equals(processorEnum.getType()))
                .findFirst()
                .orElse(null);
    }
}
