package com.tal.sea.seaover.application.dto.request.audio;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
public class AudioReportV2Request {

    //音频id(必填)
    @NotBlank(message = "Audio unique identifier cannot be blank")
    private String audioId;
    //年龄段id(非必填)
    private Long ageRangeId;
    //风格类型id(非必填)
    private Integer genresId;
    //专辑id(非必填)
    private Long albumId;
    //日间夜间(非必填)
    private Integer sceneMode;
    //播放时间
    @NotNull(message = "Listen time cannot be null")
    private Long listenTime;
    //时长
    private Long duration;
    private String talId;
}