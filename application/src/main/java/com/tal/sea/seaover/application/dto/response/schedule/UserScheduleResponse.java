package com.tal.sea.seaover.application.dto.response.schedule;

import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.ResourceTypeEnum;
import com.tal.sea.seaover.application.enums.ResourcesBusTypeEnum;
import com.tal.sea.seaover.application.service.resources.CubeResourcesService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class UserScheduleResponse extends UserSchedule {
    private String iconUrl;

    public void setIconAndRingUrl(CubeResourcesService cubeResourcesService) {
        if (StringUtils.isNotEmpty(super.getIconId())) {
            this.setIconUrl(cubeResourcesService.getUrlByFileId(ResourcesBusTypeEnum.SCHEDULE, ResourceTypeEnum.ICON, super.getIconId()));
        }
    }
}
