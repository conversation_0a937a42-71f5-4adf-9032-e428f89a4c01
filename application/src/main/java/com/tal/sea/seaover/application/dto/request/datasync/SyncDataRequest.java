package com.tal.sea.seaover.application.dto.request.datasync;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SyncDataRequest implements Serializable {

    @NotNull(message = "type cannot be empty")
    private Integer type;

    @Valid
    private List<SyncDataParentRequest> datas;

    /**
     * 闹钟同步请求
     */
    @Data
    public static class AlarmSyncRequest extends SyncDataParentRequest implements Serializable {
        @NotBlank(message = "alarmName cannot be empty")
        private String alarmName;

        @NotNull(message = "alarmTime cannot be empty")
        private Integer alarmTime;

        private String repeatDays;

        @NotNull(message = "repeating cannot be empty")
        private Integer repeating;

        private String alarmDay;

        @NotBlank(message = "iconId cannot be empty")
        private String iconId;

        @NotBlank(message = "ringId cannot be empty")
        private String ringId;

        @NotNull(message = "enabled cannot be empty")
        private Integer enabled;

        @NotNull(message = "isPreSet cannot be empty")
        private Integer isPreSet;

        /**
         * 预置闹钟类型 预制闹钟类型 0-其他 1-wakeUp 2-sleep
         */
        private Integer preSetType;
    }

    /**
     * 日程同步请求
     */
    @Data
    public static class ScheduleSyncRequest extends SyncDataParentRequest implements Serializable {
        @NotBlank(message = "name cannot be empty")
        private String name;

        @NotNull(message = "scheduleTime cannot be empty")
        private Integer scheduleTime;

        @NotNull(message = "notifyDuration cannot be empty")
        private Integer notifyDuration;

        private String repeatDays;

        @NotNull(message = "repeating cannot be empty")
        private Integer repeating;

        private String scheduleDay;

        @NotBlank(message = "iconId cannot be empty")
        private String iconId;

        @NotBlank(message = "colour cannot be empty")
        private String colour;

        @NotNull(message = "enabled cannot be empty")
        private Integer enabled;

        @NotNull(message = "isPreSet cannot be empty")
        private Integer isPreSet;

        /**
         * 预置日程类型
         */
        private Integer preSetType;
    }

    /**
     * 夜灯配置同步请求
     */
    @Data
    public static class NightLightConfigSyncRequest extends SyncDataParentRequest implements Serializable {
        @NotNull(message = "brightness cannot be empty")
        private Integer brightness;

        @NotNull(message = "lightingEffects cannot be empty")
        private Integer lightingEffects;

        @NotNull(message = "autoLight cannot be empty")
        private Integer autoLight;

        @NotBlank(message = "color cannot be empty")
        private String color;
    }

    /**
     * 宠物同步请求
     */
    @Data
    public static class PetsSyncRequest extends SyncDataParentRequest implements Serializable {
        @NotNull(message = "petId cannot be empty")
        private Integer petId;

        private String petName;

        @NotNull(message = "petLevel cannot be empty")
        private Integer petLevel;

        @NotNull(message = "selected cannot be empty")
        private Integer selected;

        @NotNull(message = "hunger cannot be empty")
        private BigDecimal hunger;

        @NotNull(message = "unhappy cannot be empty")
        private BigDecimal unhappy;

        @NotNull(message = "dirt cannot be empty")
        private BigDecimal dirt;

        @NotNull(message = "rp cannot be empty")
        private BigDecimal rp;

        @NotNull(message = "wakeupAlarmCount cannot be empty")
        private Integer wakeupAlarmCount;

        @NotNull(message = "sleepAlarmCount cannot be empty")
        private Integer sleepAlarmCount;
    }
}