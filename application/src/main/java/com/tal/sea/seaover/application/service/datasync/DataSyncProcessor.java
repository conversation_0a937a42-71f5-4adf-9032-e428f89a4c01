package com.tal.sea.seaover.application.service.datasync;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;

import java.util.List;

/**
 * 数据同步处理器接口
 */
public interface DataSyncProcessor<R extends SyncDataParentRequest, T extends SyncDataParentResponse> {
    /**
     * 获取处理的数据类型
     */
    String getType();

    /**
     * 执行同步处理
     */
    SyncResult<T> process(String talId, List<R> deviceDatas);
}