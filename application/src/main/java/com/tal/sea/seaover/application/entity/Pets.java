package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户宠物实体
 *
 * @TableName tb_user_pets
 */
@TableName(value = "tb_user_pets")
@Data
public class Pets implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户tal_id
     */
    private String talId;

    /**
     * 宠物ID
     */
    private Integer petId;

    /**
     * 宠物名称
     */
    private String petName;

    /**
     * 宠物等级，1-4
     */
    private Integer petLevel;

    /**
     * 是否选中，0-未选中，1-选中
     */
    private Integer selected;

    /**
     * 饥饿度
     */
    private BigDecimal hunger;

    /**
     * 不开心度
     */
    private BigDecimal unhappy;

    /**
     * 不洁净
     */
    private BigDecimal dirt;

    /**
     * 交互值
     */
    private BigDecimal rp;

    /**
     * 起床闹钟
     */
    private Integer wakeupAlarmCount;

    /**
     * 睡觉闹钟
     */
    private Integer sleepAlarmCount;

    /**
     * 唯一union_id
     */
    private String unionId;

    /**
     * 此数据对应的设备端中的ID值
     */
    private Integer clientId;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 是否已删除，0-否，1-是，忽略数据库映射
     */
    private Integer deleted;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}