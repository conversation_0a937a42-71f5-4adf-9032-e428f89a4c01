package com.tal.sea.seaover.application.dto.request.datasync;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据同步父类请求
 */
@Data
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type",
        defaultImpl = SyncDataParentRequest.class
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = SyncDataRequest.AlarmSyncRequest.class, name = "1"),
        @JsonSubTypes.Type(value = SyncDataRequest.ScheduleSyncRequest.class, name = "2"),
        @JsonSubTypes.Type(value = SyncDataRequest.NightLightConfigSyncRequest.class, name = "3"),
        @JsonSubTypes.Type(value = SyncDataRequest.PetsSyncRequest.class, name = "4")
})
public class SyncDataParentRequest implements Serializable {
    /**
     * 数据ID
     */
    private Long id;

    /**
     * 数据类型
     */
    private Integer type;

    /**
     * 设备端维护的此数据ID值
     */
    private Integer clientId;

    /**
     * 用户talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * 用户unionId（部分数据类型可选）
     */
    @NotBlank(message = "unionId cannot be empty")
    private String unionId;

    /**
     * 对应最近同步成功之后的服务端version
     */
    @NotNull(message = "baseVersion cannot be empty")
    private Integer baseVersion;

    /**
     * 设备端离线期间编辑产生的版本(基于baseVersion递增)
     */
    @NotNull(message = "clientVersion cannot be empty")
    private Integer clientVersion;

    /**
     * 是否已删除，0-否，1-是（部分数据类型可选）
     */
    @NotNull(message = "deleted cannot be empty")
    private Integer deleted;
}