package com.tal.sea.seaover.application.dto.response.audio;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
public class AudioDTO {
    //音频id
    private String audioId;
    //音频名称
    private String audioName;
    //情景模式，1-日间模式，2-哄睡模式
    private Integer sceneMode;
    //音频内容类型，1-故事，2-音乐，3-播客
    private Long genresId;
    //主题描述
    private String subjectDesc;
    //音频封面
    private String coverUrl;
    //音频地址
    private String audioUrl;
    //播放顺序
    private Integer sequence;
}