package com.tal.sea.seaover.application.service.pets;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.dto.response.pets.PetHousePartsResponse;
import com.tal.sea.seaover.application.entity.UserPetHouseParts;
import com.tal.sea.seaover.application.mapper.UserPetHousePartsMapper;
import com.tal.sea.seaover.application.util.RedissonLockUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * 宠物小屋装扮服务
 */
@Slf4j
@Service
@AllArgsConstructor
public class PetHousePartsService {

    private UserPetHousePartsMapper userPetHousePartsMapper;
    private RedissonLockUtil redissonLockUtil;

    /**
     * 获取宠物小屋装扮数据
     *
     * @param talId 用户ID
     * @return 宠物小屋装扮响应数据
     */
    public PetHousePartsResponse getHouseParts(String talId, Integer petId) {
        log.info("获取宠物小屋装扮数据，talId: {}, petId: {}", talId, petId);

        PetHousePartsResponse response = new PetHousePartsResponse();
        // 查询用户宠物小屋装扮数据
        UserPetHouseParts houseParts = getUserPetHouseParts(talId, petId);
        if (houseParts != null && StringUtils.hasText(houseParts.getSelectProp())) {
            response.setPetId(houseParts.getPetId());
            response.setPbgId(houseParts.getPbgId());
            response.setProps(houseParts.getSelectProp());
        }
        return response;
    }

    /**
     * 根据用户ID和宠物ID获取宠物小屋装扮数据
     *
     * @param talId 用户ID
     * @param petId 宠物ID
     * @return 宠物小屋装扮数据
     */
    public UserPetHouseParts getUserPetHouseParts(String talId, Integer petId) {
        LambdaQueryWrapper<UserPetHouseParts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPetHouseParts::getTalId, talId)
                .eq(UserPetHouseParts::getPetId, petId)
                .orderByDesc(UserPetHouseParts::getUpdateTime)
                .last("limit 1");

        return userPetHousePartsMapper.selectOne(queryWrapper);
    }

    /**
     * 保存宠物小屋装扮数据
     *
     * @param talId      用户ID
     * @param petId      宠物ID
     * @param selectProp 装扮数据（JSON格式）
     */

    public void saveHousePartsByLock(String talId, Integer petId, String pbgId, String selectProp) {
        String lockKey = "pet:house:parts:" + talId + ":" + petId;
        redissonLockUtil.executeWithLock(lockKey, () -> {
            saveHouseParts(talId, petId, pbgId, selectProp);
            return null;
        });
    }

    //分布式锁
    public void saveHouseParts(String talId, Integer petId, String pbgId, String selectProp) {
        // 查询是否已存在装扮数据
        UserPetHouseParts existingParts = getUserPetHouseParts(talId, petId);

        if (existingParts != null) {
            // 更新现有数据
            existingParts.setPbgId(pbgId);
            existingParts.setSelectProp(selectProp);
            int updateResult = userPetHousePartsMapper.updateById(existingParts);
            log.info("更新宠物小屋装扮数据，talId: {}, petId: {}, result: {}",
                    talId, petId, updateResult > 0 ? "成功" : "失败");
        } else {
            // 创建新数据
            UserPetHouseParts newParts = new UserPetHouseParts();
            newParts.setTalId(talId);
            newParts.setPetId(petId);
            newParts.setPbgId(pbgId);
            newParts.setSelectProp(selectProp);
            int insertResult = userPetHousePartsMapper.insert(newParts);
            log.info("新增宠物小屋装扮数据，talId: {}, petId: {}, result: {}",
                    talId, petId, insertResult > 0 ? "成功" : "失败");
        }
    }
}