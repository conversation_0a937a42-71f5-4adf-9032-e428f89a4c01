package com.tal.sea.seaover.application.dto.request.datasync;

import lombok.Data;

@Data
public class UserAlarmsSyncDataRequest extends SyncDataParentRequest {
    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * 铃声ID
     */
    private String ringId;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    private Integer isPreSet;

}
