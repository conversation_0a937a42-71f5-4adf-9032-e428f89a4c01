package com.tal.sea.seaover.application.service.valid;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PbgIdExistsValidator.class)
public @interface PbgIdExists {
    String message() default "背景ID不存在";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

