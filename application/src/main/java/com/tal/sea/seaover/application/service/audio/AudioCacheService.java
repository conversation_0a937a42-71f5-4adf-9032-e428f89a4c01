package com.tal.sea.seaover.application.service.audio;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.entity.Audio;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.AudioMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class AudioCacheService {

    public static final String AUDIO_CACHE_KEY_PREFIX = "audio:scene_mode:";
    private static final String CACHE_INIT_LOCK = "audio:cache:init:lock";

    @Autowired
    private AudioMapper audioMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${cdn.domain:}")
    private String cdnDomain;

    /**
     * 在Bean初始化后自动刷新缓存
     */
    @PostConstruct
    public void init() {
        refreshAudioCache();
    }

    /**
     * 刷新Redis缓存中的音频数据，按sceneMode分组存储到SortedSet
     */
    public void refreshAudioCache() {
        executeWithLock(() -> {
            // 清空现有缓存
            redissonClient.getKeys().deleteByPattern(AUDIO_CACHE_KEY_PREFIX + "*");
            // 查询所有音频数据
            List<Audio> audios = listAudios();
            if (!audios.isEmpty()) {
                // 按sceneMode分组并存入SortedSet
                groupAndStoreAudios(audios);
            }
        });
    }

    /**
     * 执行带分布式锁的操作
     *
     * @param cacheUpdateLogic 缓存更新逻辑
     */
    private void executeWithLock(Runnable cacheUpdateLogic) {
        RLock lock = redissonClient.getLock(CACHE_INIT_LOCK);
        try {
            boolean tryLock = lock.tryLock();
            if (tryLock) {
                cacheUpdateLogic.run();
            } else {
                log.warn("无法获取分布式锁，暂时放弃缓存操作");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 查询所有音频数据
     *
     * @return 音频列表
     */
    private List<Audio> listAudios() {
        LambdaQueryWrapper<Audio> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(Audio::getSceneMode, Audio::getSequence);
        return audioMapper.selectList(wrapper);
    }

    /**
     * 按sceneMode分组并存储到SortedSet
     *
     * @param audios 音频列表
     */
    private void groupAndStoreAudios(List<Audio> audios) {
        // 按sceneMode分组
        Map<Integer, List<Audio>> audioBySceneMode = audios.stream()
                .collect(Collectors.groupingBy(Audio::getSceneMode));

        // 存入Redis SortedSet
        audioBySceneMode.forEach((sceneMode, audioList) -> {
            String cacheKey = AUDIO_CACHE_KEY_PREFIX + sceneMode;
            RScoredSortedSet<Audio> sortedSet = redissonClient.getScoredSortedSet(cacheKey);
            audioList.forEach(audio -> {
                audio.setAudioUrl(cdnDomain + audio.getAudioUrl());
                audio.setCoverUrl(cdnDomain + audio.getCoverUrl());
                sortedSet.add(audio.getSequence(), audio);
            });
        });
    }

    /**
     * 判断指定场景模式下的音频缓存是否为空
     *
     * @param sceneMode 场景模式
     * @return true: 缓存不为空，false: 缓存为空
     */
    public boolean isAudioCacheEmpty(int sceneMode) {
        String cacheKey = AUDIO_CACHE_KEY_PREFIX + sceneMode;
        RScoredSortedSet<Audio> sortedSet = redissonClient.getScoredSortedSet(cacheKey);
        return !sortedSet.isEmpty();
    }


    /**
     * 根据场景模式和起始序号查询音频列表（正向或反向）
     * <p>
     * 该方法实现了循环播放的逻辑：
     * - 正向查询：从起始位置向后查询，如果到达末尾仍需更多数据，则从头开始继续查询
     * - 反向查询：从起始位置向前查询，如果到达开头仍需更多数据，则从尾部开始继续查询
     * <p>
     * 举例说明：
     * 假设音频序号为 [1,2,3,4,5,6,7,8,9,10]
     * <p>
     * 正向查询示例：startSequence=7, count=6, forward=true
     * 结果：[7,8,9,10,1,2] （先取7-10，再循环取1-2）
     * <p>
     * 反向查询示例：startSequence=3, count=5, forward=false
     * 结果：[3,2,1,10,9] （先取3-1，再循环取10-9）
     *
     * @param sceneMode     场景模式（1-日间模式，2-哄睡模式）
     * @param startSequence 起始序号（播放顺序）
     * @param count         查询数量
     * @param forward       是否正向（true: 正向查询, false: 反向查询）
     * @return 音频列表，按查询方向和循环逻辑排序
     */
    public List<Audio> getAudioRange(int sceneMode, int startSequence, int count, boolean forward) {
        String cacheKey = AUDIO_CACHE_KEY_PREFIX + sceneMode;
        // 获取对应场景模式的音频
        RScoredSortedSet<Audio> sortedSet = redissonClient.getScoredSortedSet(cacheKey);
        if (sortedSet.isEmpty()) {
            return new ArrayList<>();
        }
        // 获取音频集合中的最小和最大sequence值，用于确定查询边界
        Double minSequence = sortedSet.firstScore();  // 最小sequence
        Double maxSequence = sortedSet.lastScore();    // 最大sequence

        // 初始化结果列表和剩余查询数量
        List<Audio> result = new ArrayList<>();
        int remainingCount = count;
        if (forward) {
            // 使用valueRange进行正向范围查询
            // 参数说明：起始值，是否包含起始值，结束值，是否包含结束值，偏移量，限制数量
            Collection<Audio> firstBatch = sortedSet.valueRange(
                    startSequence, true,
                    Double.MAX_VALUE, true,
                    0, count
            );
            result.addAll(firstBatch);
            remainingCount -= firstBatch.size();  // 更新剩余查询数量

            // 第二阶段：如果还需要更多音频，从头开始循环查询
            if (remainingCount > 0) {
                // 从最小序号开始查询剩余数量的音频
                Collection<Audio> secondBatch = sortedSet.valueRange(
                        minSequence, true,
                        Double.MAX_VALUE, true,
                        0, remainingCount
                );
                result.addAll(secondBatch);
            }
        } else {
            // ==================== 反向查询逻辑 ====================
            // 反向查询：从startSequence开始向前查询，序号递减
            // 使用valueRangeReversed进行反向范围查询
            // 参数说明：起始值，是否包含起始值，结束值，是否包含结束值，偏移量，限制数量
            Collection<Audio> firstBatch = sortedSet.valueRangeReversed(
                    minSequence, true,      // 从minSequence开始（包含）
                    startSequence, true,    // 到startSequence结束（包含）
                    0, count      // 偏移量0，限制数量firstBatchCount
            );
            result.addAll(firstBatch);
            remainingCount -= firstBatch.size();  // 更新剩余查询数量

            // 第二阶段：如果还需要更多音频，从尾部开始循环查询
            if (remainingCount > 0) {
                // 从最大序号开始反向查询剩余数量的音频
                Collection<Audio> secondBatch = sortedSet.valueRangeReversed(
                        maxSequence - remainingCount + 1, true,
                        Double.MAX_VALUE, true,
                        0, remainingCount
                );
                result.addAll(secondBatch);
            }
        }

        return result;
    }
}