package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * SHA-256加密工具类
 */
@Slf4j
public class SHA256Util {

    /**
     * 对字符串进行SHA-256加密
     *
     * @param input 待加密的字符串
     * @return 加密后的十六进制字符串
     */
    public static String encrypt(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 algorithm not available", e);
            throw new RuntimeException("SHA-256 encryption failed", e);
        }
    }

    /**
     * 对字符串进行两次SHA-256加密
     *
     * @param input 待加密的字符串
     * @return 两次加密后的十六进制字符串
     */
    public static String encryptTwice(String input) {
        String firstEncrypt = encrypt(input);
        return encrypt(firstEncrypt);
    }
}
