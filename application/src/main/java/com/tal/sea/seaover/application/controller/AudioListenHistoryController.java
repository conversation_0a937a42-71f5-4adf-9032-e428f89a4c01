package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.request.audio.AddAudioListenHistoryRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioListV2Request;
import com.tal.sea.seaover.application.dto.request.audio.AudioReportV2Request;
import com.tal.sea.seaover.application.dto.response.audio.AudioListResponse;
import com.tal.sea.seaover.application.service.audio.AudioListenHistoryService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 音频收听历史Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/listenHistory")
public class AudioListenHistoryController {

    @Autowired
    private AudioListenHistoryService audioListenHistoryService;

    @PostMapping("/add")
    public ResponseEntity add(@RequestHeader("X-Tal-Id") String talId, @Valid @RequestBody AddAudioListenHistoryRequest request) {
        request.setTalId(talId);
        audioListenHistoryService.add(request);
        return ResponseUtil.successWithoutData();
    }



    @PostMapping("/add/v2")
    public ResponseEntity<Boolean> addV2(@RequestHeader("X-Tal-Id") String talId,@Valid @RequestBody AudioReportV2Request request) {
        request.setTalId(talId);
        return ResponseUtil.successWithData(audioListenHistoryService.addV2(request));
    }
}