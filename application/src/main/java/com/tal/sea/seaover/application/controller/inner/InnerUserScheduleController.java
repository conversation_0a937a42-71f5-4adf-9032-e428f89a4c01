package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.request.schedules.*;
import com.tal.sea.seaover.application.dto.response.schedule.UserScheduleResponse;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.userschedule.UserScheduleService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/inner/datahub/cube/schedule")
public class InnerUserScheduleController {
    @Autowired
    private UserScheduleService userScheduleService;
    @Resource
    private AlarmService alarmService;

    /**
     * 新增用户日程
     */
    @PostMapping("/add")
    public ResponseEntity addSchedule(@RequestBody @Validated ScheduleAddRequest scheduleAddRequest) {
        try {
            UserScheduleResponse scheduleResponse = userScheduleService.addSchedule(scheduleAddRequest);
            return ResponseUtil.successWithData(scheduleResponse);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("add schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("add schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.ADD_SCHEDULE_FAIL.getMsg());
        }
    }

    /**
     * 获取用户日程
     */
    @PostMapping("/get")
    public ResponseEntity<UserScheduleResponse> getSchedule(@RequestBody @Validated GetScheduleRequest getScheduleRequest) {
        UserScheduleResponse scheduleResponse = userScheduleService.getScheduleResponse(getScheduleRequest.getScheduleId());
        return ResponseUtil.successWithData(scheduleResponse);
    }

    /**
     * 获取用户开启日程数量
     */
    @PostMapping("/getOpenCount")
    public ResponseEntity<Integer> getOpenCount(@RequestBody @Validated GetOpenCountRequest getOpenCountRequest) {
        Integer result = userScheduleService.getOpenCount(getOpenCountRequest);
        return ResponseUtil.successWithData(result);
    }

    /**
     * 获取用户日程列表
     */
    @PostMapping("/list")
    public ResponseEntity<List<UserScheduleResponse>> scheduleList(@RequestBody @Validated ScheduleListRequest scheduleListRequest) {
        List<UserScheduleResponse> result = userScheduleService.scheduleList(scheduleListRequest);
        return ResponseUtil.successWithData(result);
    }

    /**
     * 设置日程状态
     */
    @PostMapping("/setEnabled")
    public ResponseEntity setEnabled(@RequestBody @Validated SetScheduleEnabledRequest setScheduleEnabledRequest) {
        try {
            UserSchedule result = userScheduleService.setEnabled(setScheduleEnabledRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("set schedule enabled fail! exception:{}", e.getMessage());
            alarmService.alarm("set schedule enabled fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.SET_SCHEDULE_ENABLED_FAIL.getMsg());
        }
    }

    /**
     * 编辑日程
     */
    @PostMapping("/edit")
    public ResponseEntity edit(@RequestBody @Validated ScheduleEditRequest scheduleEditRequest) {
        try {
            UserSchedule result = userScheduleService.edit(scheduleEditRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("edit schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500("edit schedule fail!");
        }
    }

    /**
     * 删除用户日程
     */
    @PostMapping("/delete")
    public ResponseEntity deleteSchedule(@RequestBody @Validated DeleteScheduleRequest deleteScheduleRequest) {
        try {
            UserSchedule result = userScheduleService.deleteSchedule(deleteScheduleRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("delete schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("delete schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.DELETE_SCHEDULE_FAIL.getMsg());
        }
    }

    /**
     * 批量查询日程
     */
    @PostMapping("/batchListByUuIds")
    public ResponseEntity<List<UserScheduleResponse>> batchListByUuIds(@RequestBody @Validated ScheduleBatchListRequest request) {
        try {
            List<UserScheduleResponse> result = userScheduleService.batchListByUuids(request.getUuids());
            return ResponseUtil.successWithData(result);
        } catch (Exception e) {
            log.error("batch list schedules fail! exception:{}", e.getMessage());
            alarmService.alarm("batch list schedules fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500("batch list schedules fail!");
        }
    }
}