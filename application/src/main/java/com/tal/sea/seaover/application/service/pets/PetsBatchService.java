package com.tal.sea.seaover.application.service.pets;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.Pets;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.mapper.PetsMapper;
import com.tal.sea.seaover.application.util.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class PetsBatchService extends ServiceImpl<PetsMapper, Pets> implements IService<Pets> {
    /**
     * 批量添加
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean saveBatch(List<Pets> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 批量更新
     *
     * @param entityList
     */
    @Transactional
    public void updateBatchByIdForDevice(List<Pets> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(pet -> {
            pet.setUpdateTime(new Date());
            pet.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            //pet.setVersion(pet.getVersion() + 1);
        });
        super.updateBatchById(entityList);
    }

    /**
     * 根据talId查询用户宠物记录
     *
     * @param talId 用户ID
     * @return 用户宠物记录列表
     */
    public List<Pets> getByTalId(String talId) {
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新用户宠物记录的talId和删除标记
     *
     * @param entityList 实体列表
     * @param encryptedTalId 加密后的talId
     * @return 更新是否成功
     */
    @Transactional
    public boolean updateBatchForUserLogout(List<Pets> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("Pets entityList is empty, skip update");
            return true;
        }

        Date now = new Date();
        entityList.forEach(entity -> {
            // 设置加密后的talId
            entity.setTalId(encryptedTalId);
            // 设置删除标记为1
            entity.setDeleted(1);
            // 设置更新时间
            entity.setUpdateTime(now);
        });

        boolean result = super.updateBatchById(entityList);
        log.info("Pets batch update completed, count: {}, result: {}", entityList.size(), result);
        return result;
    }

    /**
     * 处理用户登出时的用户宠物数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing Pets data for user logout, talId: {}", talId);

        // 查询该用户的所有宠物记录
        List<Pets> petsList = getByTalId(talId);

        if (CollectionUtils.isEmpty(petsList)) {
            log.info("No Pets found for talId: {}", talId);
            return;
        }
        // 批量更新
        updateBatchForUserLogout(petsList, encryptedTalId);

        log.info("Pets data processing completed for talId: {}, processed count: {}", talId, petsList.size());
    }
}
