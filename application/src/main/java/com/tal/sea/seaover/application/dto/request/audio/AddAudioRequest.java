package com.tal.sea.seaover.application.dto.request.audio;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AddAudioRequest {

    @NotNull(message = "Scene mode cannot be null")
    @Min(value = 1, message = "Scene mode must be 1 or 2")
    private Integer sceneMode;

    @NotNull(message = "Content type cannot be null")
    @Min(value = 1, message = "Content type must be 1, 2 or 3")
    private Integer contentType;

    @NotBlank(message = "Audio name cannot be blank")
    private String name;

    private String subjectDesc;

    @NotBlank(message = "Cover URL cannot be blank")
    private String coverUrl;

    @NotBlank(message = "Audio URL cannot be blank")
    private String audioUrl;

    @NotNull(message = "Sequence cannot be null")
    @Min(value = 0, message = "Sequence cannot be negative")
    private Integer sequence;
}