package com.tal.sea.seaover.application.dto.response.syncdata;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SyncStagingData<R> {

    private String talId;
    /**
     * 服务端添加成功需要客户端更改本地的ID和version【设备端】
     */
    private List<String> deviceAdded;
    /**
     * 服务端有新增需要设备端做本地新增【设备端】
     */
    private List<String> deviceToAdd;
    /**
     * 服务端有修改需要设备端做本地修改【设备端】
     */
    private List<String> deviceToUpdate;
    /**
     * 服务端根据设备的数据做了修改，需要客户端更新本地的version【设备端】
     */
    private List<String> deviceToUpdateVersion;
    /**
     * 和服务端有冲突或者服务端有删除需要设备端做本地删除【设备端】
     */
    private List<String> deviceToDelete; // 客户端需删除的记录

    /**
     * 设备端提交的数据
     */
    private List<R> deviceDatas;


    /**
     * 需要服务端做新增的
     */
    private List<R> serverToAdd;
    /**
     * 需要服务端做修改的
     */
    private List<R> serverToUpdate;
    /**
     * 需要服务端做删除的
     */
    private List<R> serverToDelete;


    public SyncStagingData(String talId, List<R> deviceDatas) {
        this.talId = talId;
        this.deviceAdded = new ArrayList<>();
        this.deviceToAdd = new ArrayList<>();
        this.deviceToUpdate = new ArrayList<>();
        this.deviceToUpdateVersion = new ArrayList<>();
        this.deviceToDelete = new ArrayList<>();

        this.serverToAdd = new ArrayList<>();
        this.serverToUpdate = new ArrayList<>();
        this.serverToDelete = new ArrayList<>();
        this.deviceDatas = deviceDatas;
    }

    public SyncStagingData() {
    }


    /**
     * 从 deviceAdded 列表中移除指定元素，如果存在则移除并返回 true，否则返回 false。
     *
     * @param element 要移除的元素
     */
    public void removeFromDeviceAdded(String element) {
        if (deviceAdded == null) {
            return;
        }
        deviceAdded.remove(element);
    }
}

