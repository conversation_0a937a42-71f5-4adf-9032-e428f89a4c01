package com.tal.sea.seaover.application.service.datasync;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * SyncDataRequest 转换器
 */
@Component
public class SyncDataRequestConverter {

    /**
     * 转换闹钟数据
     */
    public List<SyncDataRequest.AlarmSyncRequest> convertAlarms(List<SyncDataRequest.AlarmSyncRequest> alarmDataList) {
        List<SyncDataRequest.AlarmSyncRequest> alarmSyncRequests = alarmDataList != null ? alarmDataList : new ArrayList<>();
        for (SyncDataRequest.AlarmSyncRequest requestParameter : alarmSyncRequests){
            //如果是一次性闹钟默认将alarmDay对应的周几放入到repeatDays
            if (requestParameter.getRepeating() == YesNoEnum.NO.getValue()) {
                String alarmDay = requestParameter.getAlarmDay();
                if (StringUtils.isNotEmpty(alarmDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(alarmDay);
                    requestParameter.setRepeatDays(dayOfWeek);
                }
            }
        }
        return alarmSyncRequests;
    }

    /**
     * 转换日程数据
     */
    public List<SyncDataRequest.ScheduleSyncRequest> convertSchedules(List<SyncDataRequest.ScheduleSyncRequest> scheduleDataList) {
        List<SyncDataRequest.ScheduleSyncRequest> scheduleSyncRequests = scheduleDataList != null ? scheduleDataList : new ArrayList<>();
        for (SyncDataRequest.ScheduleSyncRequest requestParameter : scheduleSyncRequests){
            //如果是一次性日程默认将scheduleDay对应的周几放入到repeatDays
            if (requestParameter.getRepeating() == YesNoEnum.NO.getValue()) {
                String alarmDay = requestParameter.getScheduleDay();
                if (StringUtils.isNotEmpty(alarmDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(alarmDay);
                    requestParameter.setRepeatDays(dayOfWeek);
                }
            }
        }
        return scheduleSyncRequests;
    }

    /**
     * 转换夜灯配置数据
     */
    public List<SyncDataRequest.NightLightConfigSyncRequest> convertNightLightConfigs(List<SyncDataRequest.NightLightConfigSyncRequest> configDataList) {
        return configDataList != null ? configDataList : new ArrayList<>();
    }

    /**
     * 转换宠物数据
     */
    public List<SyncDataRequest.PetsSyncRequest> convertPets(List<SyncDataRequest.PetsSyncRequest> petDataList) {
        return petDataList != null ? petDataList : new ArrayList<>();
    }
}