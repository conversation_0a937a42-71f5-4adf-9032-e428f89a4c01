package com.tal.sea.seaover.application.service.datasync.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest.PetsSyncRequest;
import com.tal.sea.seaover.application.dto.response.pets.PetsSyncDataResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import com.tal.sea.seaover.application.entity.Pets;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.service.datasync.AbstractCubeDataSyncProcess;
import com.tal.sea.seaover.application.service.datasync.helper.SyncResultHelper;
import com.tal.sea.seaover.application.service.pets.PetsBatchService;
import com.tal.sea.seaover.application.service.pets.PetsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PetsDataSyncImpl extends AbstractCubeDataSyncProcess<PetsSyncRequest, PetsSyncDataResponse> {
    @Autowired
    private PetsService petsService;
    @Autowired
    private PetsBatchService petsBatchService;

    @Override
    protected List<PetsSyncDataResponse> getServerExistData(String talId) {
        return petsService.listPetsSyncDataResponseByTalId(talId);
    }

    @Override
    protected List<PetsSyncDataResponse> getAllServerExistData(String talId) {
        return petsService.listPetsSyncDataResponseByTalId(talId);
    }


    @Override
    protected boolean checkConflict(List<PetsSyncDataResponse> serverExistData, PetsSyncRequest deviceData, PetsSyncDataResponse currentServerData) {
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void executeOperationDb(SyncStagingData<PetsSyncRequest> syncStagingData) {
        String talId = syncStagingData.getTalId();
        List<PetsSyncDataResponse> serverExistData = getServerExistData(talId);
        Map<Integer, PetsSyncDataResponse> petIdMap = Map.of();
        if (CollectionUtils.isNotEmpty(serverExistData)) {
            //转换为map key为：petId value为：PetsSyncDataResponse
            petIdMap = serverExistData.stream()
                    .collect(Collectors.toMap(
                            PetsSyncDataResponse::getPetId, // key为petId
                            response -> response // value为PetsSyncDataResponse
                    ));
        }

        Map<Integer, PetsSyncDataResponse> finalPetIdMap = petIdMap;
        List<Pets> batchAdd = Optional.ofNullable(syncStagingData.getServerToAdd())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    //这里处理是为了防止同一个用户的同一个宠物被添加多次，而且要去更新提交的值
                    if (MapUtil.isNotEmpty(finalPetIdMap)) {
                        Integer petId = request.getPetId();
                        PetsSyncDataResponse currentServerData = finalPetIdMap.get(petId);
                        if (currentServerData != null) {
                            request.setId(currentServerData.getId());
                            syncStagingData.getServerToUpdate().add(request);
                            syncStagingData.getDeviceToUpdateVersion().add(request.getUnionId());
                            syncStagingData.removeFromDeviceAdded(request.getUnionId());
                            return null;
                        }
                    }
                    Pets pet = new Pets();
                    BeanUtils.copyProperties(request, pet);
                    return pet;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<Pets> batchUpdate = new ArrayList<>();
        Optional.ofNullable(syncStagingData.getServerToUpdate())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    Pets pet = petsService.getById(request.getId());
                    if (pet != null) {
                        pet.setUnionId(request.getUnionId());
                        pet.setPetId(request.getPetId());
                        pet.setPetName(request.getPetName());
                        //pet.setSelected(request.getSelected());
                        pet.setHunger(request.getHunger());
                        pet.setUnhappy(request.getUnhappy());
                        pet.setDirt(request.getDirt());
                        pet.setDeleted(request.getDeleted());
                        //判断rp的值大于库里的值才可以更新
                        if (request.getRp() != null && request.getRp().compareTo(pet.getRp()) > 0) {
                            pet.setRp(request.getRp());
                        }
                        //判断wakeupAlarmCount的值大于库里的值才可以更新
                        if (request.getWakeupAlarmCount() != null && request.getWakeupAlarmCount() > pet.getWakeupAlarmCount()) {
                            pet.setWakeupAlarmCount(request.getWakeupAlarmCount());
                        }
                        //判断sleepAlarmCount的值大于库里的值才可以更新
                        if (request.getSleepAlarmCount() != null && request.getSleepAlarmCount() > pet.getSleepAlarmCount()) {
                            pet.setSleepAlarmCount(request.getSleepAlarmCount());
                        }
                        //判断等级
                        if (request.getPetLevel() != null && request.getPetLevel() > pet.getPetLevel()) {
                            pet.setPetLevel(request.getPetLevel());
                        }
                        return pet;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);

        if (!batchAdd.isEmpty()) {
            petsBatchService.saveBatch(batchAdd);
        }
        if (!batchUpdate.isEmpty()) {
            petsBatchService.updateBatchByIdForDevice(batchUpdate);
        }
        //处理宠物选中状态
        handleSelectedStatus(syncStagingData, serverExistData);
    }

    private void handleSelectedStatus(SyncStagingData<PetsSyncRequest> syncStagingData, List<PetsSyncDataResponse> serverExistData) {
        if (CollectionUtils.isEmpty(serverExistData)) {
            return;
        }
        String talId = syncStagingData.getTalId();
        // 1. 获取服务端当前选中的宠物 petId
        Integer serverSelectedPetId = serverExistData.stream()
                .filter(data -> data.getSelected() != null && data.getSelected() == YesNoEnum.YES.getValue())
                .map(PetsSyncDataResponse::getPetId)
                .findFirst()
                .orElse(null);
        log.info("服务端当前选中的宠物 petId:{}", serverSelectedPetId);
        if (Objects.isNull(serverSelectedPetId)) {
            return;
        }
        // 2. 获取提交上来的选中的宠物 petId
        Integer deviceSelectedPetId = Optional.ofNullable(syncStagingData.getDeviceDatas())
                .orElse(Collections.emptyList())
                .stream()
                .filter(req -> req.getSelected() != null && req.getSelected() == YesNoEnum.YES.getValue())
                .map(PetsSyncRequest::getPetId)
                .findFirst()
                .orElse(null);
        log.info("设备端当前选中的宠物 petId:{}", deviceSelectedPetId);
        if (Objects.isNull(deviceSelectedPetId)) {
            return;
        }
        Pets isExistPet = petsService.getByTalIdAndPedId(talId, deviceSelectedPetId);
        if (Objects.isNull(isExistPet)) {
            log.info("设备端当前选中的宠物服务端不存在 不做选中状态处理，petId:{}", deviceSelectedPetId);
            return;
        }
        // 3. 比较是否一致，不一致则更新
        if (Objects.equals(serverSelectedPetId, deviceSelectedPetId)) {
            log.info("设备端当前选中的宠物与服务端一致不做选中状态处理 serverSelectedPetId:{} deviceSelectedPetId:{}",
                    serverSelectedPetId, deviceSelectedPetId);
            return;
        }
        // 遍历服务端现有宠物，更新选中状态
        List<Pets> petsToUpdate = petsService.ListPets(talId);
        if (CollectionUtils.isNotEmpty(petsToUpdate)) {
            for (Pets pet : petsToUpdate) {
                if (pet.getPetId().equals(deviceSelectedPetId)) {
                    pet.setSelected(YesNoEnum.YES.getValue()); // 新选中宠物设为 1
                } else {
                    pet.setSelected(YesNoEnum.NO.getValue()); // 其他宠物设为 0
                }
            }
            petsBatchService.updateBatchByIdForDevice(petsToUpdate);
        }
    }

    @Override
    protected SyncResult<PetsSyncDataResponse> assemblyResultData(SyncStagingData<PetsSyncRequest> syncStagingData) {
        return SyncResultHelper.assemble(
                syncStagingData,
                (unionIds) -> getPets(syncStagingData, unionIds),
                (this::convertToPetsSyncDataResponse),
                Pets::getUnionId);
    }

    @Override
    public String getType() {
        return DataSyncProcessorEnum.PETS.getType();
    }

    private List<Pets> getPets(SyncStagingData<PetsSyncRequest> syncStagingData, Set<String> unionIds) {
        List<Pets> pets = petsService.listByUnionIds(syncStagingData.getTalId(), unionIds);
        List<PetsSyncRequest> deviceDatas = syncStagingData.getDeviceDatas();
        if (pets == null) {
            pets = new ArrayList<>();
        }
        // 提取 UserSchedules 中的 unionId 集合
        Set<String> existingUnionIds = pets.stream()
                .map(Pets::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        for (PetsSyncRequest request : deviceDatas) {
            if (!existingUnionIds.contains(request.getUnionId())) {
                // 如果不存在，则添加到结果列表中
                Pets pet = new Pets();
                BeanUtils.copyProperties(request, pet);
                pets.add(pet);
            }
        }
        return pets;
    }

    /**
     * 将 pets 转换为 PetsSyncDataResponse
     */
    private PetsSyncDataResponse convertToPetsSyncDataResponse(Pets pet) {
        if (pet == null) {
            return null;
        }
        PetsSyncDataResponse response = new PetsSyncDataResponse();
        BeanUtils.copyProperties(pet, response);
        response.setVersion(pet.getVersion()); // 确保 version 字段正确映射
        return response;
    }
}
