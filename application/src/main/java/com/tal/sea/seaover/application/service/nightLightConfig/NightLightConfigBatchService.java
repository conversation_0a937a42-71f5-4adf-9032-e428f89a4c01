package com.tal.sea.seaover.application.service.nightLightConfig;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.mapper.NightLightConfigMapper;
import com.tal.sea.seaover.application.util.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class NightLightConfigBatchService extends ServiceImpl<NightLightConfigMapper, NightLightConfig> implements IService<NightLightConfig> {
    /**
     * 批量添加
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean saveBatch(List<NightLightConfig> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 批量更新
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean updateBatchByIdForDevice(List<NightLightConfig> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        entityList.forEach(data -> {
            data.setUpdateTime(new Date());
            data.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            data.setVersion(data.getVersion() + 1);
        });
        return super.updateBatchById(entityList);
    }

    /**
     * 根据talId查询夜灯配置记录
     *
     * @param talId 用户ID
     * @return 夜灯配置记录列表
     */
    public List<NightLightConfig> getByTalId(String talId) {
        LambdaQueryWrapper<NightLightConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NightLightConfig::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新夜灯配置记录的talId和删除标记
     *
     * @param entityList 实体列表
     * @param encryptedTalId 加密后的talId
     * @return 更新是否成功
     */
    @Transactional
    public boolean updateBatchForUserLogout(List<NightLightConfig> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("NightLightConfig entityList is empty, skip update");
            return true;
        }

        Date now = new Date();
        entityList.forEach(entity -> {
            // 设置加密后的talId
            entity.setTalId(encryptedTalId);
            // 设置删除标记为1
            entity.setDeleted(1);
            // 设置更新时间
            entity.setUpdateTime(now);
        });

        boolean result = super.updateBatchById(entityList);
        log.info("NightLightConfig batch update completed, count: {}, result: {}", entityList.size(), result);
        return result;
    }

    /**
     * 处理用户登出时的夜灯配置数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing NightLightConfig data for user logout, talId: {}", talId);

        // 查询该用户的所有夜灯配置记录
        List<NightLightConfig> configList = getByTalId(talId);

        if (CollectionUtils.isEmpty(configList)) {
            log.info("No NightLightConfig found for talId: {}", talId);
            return;
        }
        // 批量更新
        updateBatchForUserLogout(configList, encryptedTalId);

        log.info("NightLightConfig data processing completed for talId: {}, processed count: {}", talId, configList.size());
    }
}
