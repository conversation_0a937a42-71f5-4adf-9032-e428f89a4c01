package com.tal.sea.seaover.application.service.nightLightConfig;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.constant.RedisConstant;
import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigEditForDeviceRequest;
import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigEditRequest;
import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigGetRequest;
import com.tal.sea.seaover.application.dto.response.nightLightConfig.NightLightConfigSyncDataResponse;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.enums.DeleteStatusEnum;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.mapper.NightLightConfigMapper;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.iot.IotService;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class NightLightConfigService {


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NightLightConfigMapper nightLightConfigMapper;

    @Autowired
    private AlarmService alarmService;
    @Autowired
    private IotService iotService;

    /**
     * 查询夜灯配置
     *
     * @param request 请求参数
     * @return 夜灯配置
     * @throws Exception 异常
     */
    public NightLightConfig getNightLightConfig(NightLightConfigGetRequest request) {
        request.validate();
        LambdaQueryWrapper<NightLightConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (request.getNightLightConfigId() != null) {
            queryWrapper.eq(NightLightConfig::getId, request.getNightLightConfigId());
        } else {
            queryWrapper.eq(NightLightConfig::getTalId, request.getTalId());
        }
        NightLightConfig nightLightConfig = nightLightConfigMapper.selectOne(queryWrapper);
        //满足 家长端查询为空时并且是使用talId，才创建默认配置
        boolean isNewCreateDefault = Objects.equals(IdentityEnum.PARENT.getValue(), request.getLastModifiedBy())
                && Objects.isNull(nightLightConfig) &&
                StringUtils.isNotEmpty(request.getTalId());
        if (isNewCreateDefault) {
            nightLightConfig = addNewNightLightConfig(request.getTalId());
        }
        return nightLightConfig;
    }

    private NightLightConfig addNewNightLightConfig(String talId) {
        NightLightConfig nightLightConfig;
        NightLightConfig newNightLightConfig = new NightLightConfig();
        newNightLightConfig.setTalId(talId);
        newNightLightConfig.setClientId(0);
        newNightLightConfig.setUnionId(UUID.randomUUID().toString());
        newNightLightConfig.setBrightness(255);
        newNightLightConfig.setLightingEffects(0);
        newNightLightConfig.setAutoLight(0);
        newNightLightConfig.setColor("#ff5900");
        newNightLightConfig.setLastModifiedBy(IdentityEnum.PARENT.getValue());
        nightLightConfig = add(newNightLightConfig);
        return nightLightConfig;
    }

    @Transactional(rollbackFor = Exception.class)
    public NightLightConfig editNightLightConfig(NightLightConfigEditForDeviceRequest request) throws InterruptedException {
        log.info("editNightLightConfig request: {}", GsonUtil.toJson(request));
        //根据talId查询是否存在
        String talId = request.getTalId();
        List<NightLightConfig> nightLightConfigs = listNightLightConfig(talId);
        NightLightConfigEditRequest editRequest = new NightLightConfigEditRequest();
        if (CollectionUtils.isEmpty(nightLightConfigs)) {
            NightLightConfig newNightLightConfig = addNewNightLightConfig(talId);
            editRequest.setId(newNightLightConfig.getId());
        } else {
            editRequest.setId(nightLightConfigs.get(0).getId());
        }
        editRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
        BeanUtils.copyProperties(request, editRequest);
        return editNightLightConfig(editRequest);
    }

    /**
     * 编辑夜灯配置
     *
     * @param request 请求参数
     * @return 更新后的夜灯配置
     * @throws Exception 异常
     */
    public NightLightConfig editNightLightConfig(NightLightConfigEditRequest request) throws InterruptedException {
        log.info("editNightLightConfig request: {}", GsonUtil.toJson(request));
        RLock lock = null;
        try {
            String talId = request.getTalId();
            // 获取用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_NIGHT_LIGHT_LOCK + talId);
            boolean isLocked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("editNightLightConfig failed to acquire lock for talId: {}", talId);
                throw new BusinessException(ErrorEnum.LOCK_ACQUIRE_FAILED);
            }
            // 查询现有配置
            NightLightConfig config = nightLightConfigMapper.selectById(request.getId());
            if (Objects.isNull(config)) {
                throw new BusinessException(ErrorEnum.NIGHT_LIGHT_CONFIG_NOT_FOUND);
            }
            // 更新配置
            BeanUtils.copyProperties(request, config);
            config.setLastModifiedBy(IdentityEnum.PARENT.getValue());
            config.setUpdateTime(new Date());
            config.setVersion(config.getVersion() + 1);
            nightLightConfigMapper.updateById(config);

            //只有家长端修改了日程，才调用IOT服务
            if (Objects.equals(request.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForNightLightConfig(request.getSn(), config);
            }
            log.info("editNightLightConfig response: {}", GsonUtil.toJson(config));
            return config;
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public List<NightLightConfigSyncDataResponse> listByTalId(String talId) {
        List<NightLightConfig> nightLightConfigs = listNightLightConfig(talId);
        if (Objects.isNull(nightLightConfigs)) {
            return null;
        }
        return nightLightConfigs.stream().map(nightLightConfig -> {
            NightLightConfigSyncDataResponse nightLightConfigSyncDataResponse = new NightLightConfigSyncDataResponse();
            BeanUtils.copyProperties(nightLightConfig, nightLightConfigSyncDataResponse);
            return nightLightConfigSyncDataResponse;
        }).toList();
    }

    private List<NightLightConfig> listNightLightConfig(String talId) {
        LambdaQueryWrapper<NightLightConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NightLightConfig::getTalId, talId);
        return nightLightConfigMapper.selectList(queryWrapper);
    }

    public boolean existsByUnionId(String unionId, String talId) {
        LambdaQueryWrapper<NightLightConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NightLightConfig::getUnionId, unionId);
        queryWrapper.eq(NightLightConfig::getTalId, talId);
        return nightLightConfigMapper.selectCount(queryWrapper) > 0;
    }

    public NightLightConfig add(NightLightConfig nightLightConfig) {
        if (Objects.isNull(nightLightConfig)) {
            return null;
        }
        nightLightConfig.setVersion(1);
        nightLightConfig.setCreateTime(new Date());
        nightLightConfig.setUpdateTime(new Date());
        nightLightConfig.setDeleted(DeleteStatusEnum.NO_DELETE.getValue());
        nightLightConfigMapper.insert(nightLightConfig);
        return nightLightConfig;
    }

    public NightLightConfig getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return nightLightConfigMapper.selectById(id);
    }

    public void update(NightLightConfig nightLightConfig) {
        if (Objects.isNull(nightLightConfig)) {
            return;
        }
        nightLightConfig.setUpdateTime(new Date());
        nightLightConfig.setVersion(nightLightConfig.getVersion() + 1);
        nightLightConfigMapper.updateById(nightLightConfig);
    }

    public List<NightLightConfig> listByUnionIds(String talId, Set<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return null;
        }
        LambdaQueryWrapper<NightLightConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NightLightConfig::getUnionId, unionIds);
        queryWrapper.eq(NightLightConfig::getTalId, talId);
        return nightLightConfigMapper.selectList(queryWrapper);
    }

}
