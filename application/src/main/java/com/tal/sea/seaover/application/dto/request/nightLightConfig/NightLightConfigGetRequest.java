package com.tal.sea.seaover.application.dto.request.nightLightConfig;

import com.tal.sea.seaover.application.exception.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 查询夜灯配置请求
 */
@Data
public class NightLightConfigGetRequest implements Serializable {

    private Long nightLightConfigId;

    private String talId;

    private Integer lastModifiedBy;

    public void validate() {
        if (nightLightConfigId == null && StringUtils.isEmpty(talId)) {
            throw new BusinessException("nightLightConfigId and talId cannot both be empty");
        }
    }
}
