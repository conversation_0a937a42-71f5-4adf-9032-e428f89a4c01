package com.tal.sea.seaover.application.service.datasync.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest.NightLightConfigSyncRequest;
import com.tal.sea.seaover.application.dto.response.nightLightConfig.NightLightConfigSyncDataResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.service.datasync.AbstractCubeDataSyncProcess;
import com.tal.sea.seaover.application.service.datasync.helper.SyncResultHelper;
import com.tal.sea.seaover.application.service.nightLightConfig.NightLightConfigBatchService;
import com.tal.sea.seaover.application.service.nightLightConfig.NightLightConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NightLightConfigDataSyncImpl extends AbstractCubeDataSyncProcess<NightLightConfigSyncRequest, NightLightConfigSyncDataResponse> {
    @Autowired
    private NightLightConfigService nightLightConfigService;
    @Autowired
    private NightLightConfigBatchService nightLightConfigBatchService;

    @Override
    protected List<NightLightConfigSyncDataResponse> getServerExistData(String talId) {
        return nightLightConfigService.listByTalId(talId);
    }

    @Override
    protected List<NightLightConfigSyncDataResponse> getAllServerExistData(String talId) {
        return nightLightConfigService.listByTalId(talId);
    }


    @Override
    protected boolean checkConflict(List<NightLightConfigSyncDataResponse> serverExistData, NightLightConfigSyncRequest deviceData, NightLightConfigSyncDataResponse currentServerData) {
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void executeOperationDb(SyncStagingData<NightLightConfigSyncRequest> syncStagingData) {

        if (CollectionUtils.isNotEmpty(syncStagingData.getServerToAdd())) {
            List<NightLightConfigSyncDataResponse> serverExistConfigs = getServerExistData(syncStagingData.getTalId());
            if (CollectionUtils.isNotEmpty(serverExistConfigs)) {
                Optional.ofNullable(syncStagingData.getDeviceAdded()).ifPresent(List::clear);
                log.info("talId: {} 已经存在了夜灯配置,这里不做新增处理 {}", syncStagingData.getTalId(), serverExistConfigs);
            } else {
                // 处理 serverToAdd（新增）
                List<NightLightConfig> batchAdd = syncStagingData.getServerToAdd()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(request -> {
                            NightLightConfig nightLightConfig = new NightLightConfig();
                            BeanUtils.copyProperties(request, nightLightConfig);
                            return nightLightConfig;
                        })
                        .toList();
                if (!batchAdd.isEmpty()) {
                    nightLightConfigBatchService.saveBatch(batchAdd);
                }
            }
        }


        List<NightLightConfig> batchUpdate = new ArrayList<>();
        // 处理 serverToUpdate
        Optional.ofNullable(syncStagingData.getServerToUpdate())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    NightLightConfig config = nightLightConfigService.getById(request.getId());
                    if (Objects.nonNull(config)) {
                        BeanUtils.copyProperties(request, config);
                    }
                    return config;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);


//        // 处理 serverToDelete
//        Optional.ofNullable(syncStagingData.getServerToDelete())
//                .orElse(Collections.emptyList())
//                .stream()
//                .filter(Objects::nonNull)
//                .map(request -> {
//                    NightLightConfig config = nightLightConfigService.getById(request.getId());
//                    if (Objects.nonNull(config)) {
//                        config.setDeleted(YesNoEnum.YES.getValue());
//                        return config;
//                    }
//                    return null;
//                })
//                .filter(Objects::nonNull)
//                .forEach(batchUpdate::add);


        if (!batchUpdate.isEmpty()) {
            nightLightConfigBatchService.updateBatchByIdForDevice(batchUpdate);
        }
    }

    @Override
    protected SyncResult<NightLightConfigSyncDataResponse> assemblyResultData(SyncStagingData<NightLightConfigSyncRequest> syncStagingData) {
        return SyncResultHelper.assemble(
                syncStagingData,
                (unionIds) -> getNightLightConfigs(syncStagingData, unionIds),
                (this::convert),
                NightLightConfig::getUnionId);
    }


    private List<NightLightConfig> getNightLightConfigs(SyncStagingData<SyncDataRequest.NightLightConfigSyncRequest> syncStagingData, Set<String> unionIds) {
        List<NightLightConfig> nightLightConfigs = nightLightConfigService.listByUnionIds(syncStagingData.getTalId(), unionIds);
        if (nightLightConfigs == null) {
            nightLightConfigs = new ArrayList<>();
        }
        List<SyncDataRequest.NightLightConfigSyncRequest> deviceDatas = syncStagingData.getDeviceDatas();
        // 提取 nightLightConfigs 中的 unionId 集合
        Set<String> existingUnionIds = nightLightConfigs.stream()
                .map(NightLightConfig::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        for (SyncDataRequest.NightLightConfigSyncRequest request : deviceDatas) {
            if (!existingUnionIds.contains(request.getUnionId())) {
                // 如果不存在库中的unionId，则添加到结果列表中
                NightLightConfig nightLightConfig = new NightLightConfig();
                BeanUtils.copyProperties(request, nightLightConfig);
                nightLightConfigs.add(nightLightConfig);
            }
        }
        return nightLightConfigs;
    }


    @Override
    public String getType() {
        return DataSyncProcessorEnum.NIGHT_LIGHT.getType();
    }

    /**
     * 将 UserAlarms 转换为 UserAlarmsResponse
     */
    private NightLightConfigSyncDataResponse convert(NightLightConfig nightLightConfig) {
        if (nightLightConfig == null) {
            return null;
        }
        NightLightConfigSyncDataResponse response = new NightLightConfigSyncDataResponse();
        BeanUtils.copyProperties(nightLightConfig, response);
        response.setVersion(nightLightConfig.getVersion()); // 确保 version 字段正确映射
        return response;
    }
}
