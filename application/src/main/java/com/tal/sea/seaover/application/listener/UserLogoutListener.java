package com.tal.sea.seaover.application.listener;   // 解决package
// 默认导入lombok，方便日志打印

import com.tal.sea.seaover.application.dto.listener.UserLogoutMessage;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.userlogout.UserLogoutService;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听用户注销
 * <p>
 * 删除用户相关数据
 * </p>
 */
@Slf4j
@Service
public class UserLogoutListener {

    @Value("${spring.kafka.consumer.user-logout-topic.topic}")
    public String topicName;
    @Value("${spring.kafka.consumer.user-logout-topic.consumer-group}")
    public String groupId;

    @Autowired
    private UserLogoutService userLogoutService;

    @Autowired
    private AlarmService alarmService;

    @KafkaListener(topics = "#{__listener.topicName}", groupId = "#{__listener.groupId}")
    public void consumer(ConsumerRecord<String, String> record) {
        String key = record.key();
        String value = record.value();
        try {
            log.info("UserLogoutListener callback message, topicName:{}, key:{}, value:{}", topicName, key, value);
            UserLogoutMessage message = GsonUtil.fromJson(value, UserLogoutMessage.class);
            if (Objects.isNull(message)) {
                log.error("UserLogoutListener The received udc callback message is null, key:{}, value:{}", key, value);
                return;
            }
            String eventType = message.getEventType();
            if (!"1".equals(eventType)) {
                log.error("UserLogoutListener The received udc callback message is not a logout message, key:{}, value:{}", key, value);
                return;
            }
            List<String> talIdList = message.getData().getDetails().getTalIdList();
            if (CollectionUtils.isEmpty(talIdList)) {
                log.error("UserLogoutListener The received udc callback message has empty talIdList, key:{}, value:{}", key, value);
                return;
            }

            // 调用UserLogoutService处理用户登出数据
            userLogoutService.processUserLogoutData(talIdList);

            log.info("UserLogoutListener successfully processed {} users logout data", talIdList.size());

        } catch (Exception e) {
            log.error("UserLogoutListener consumer exception, error[{}], topic[{}], key[{}], value[{}]", e.getMessage(), record.topic(), key, value, e);
            alarmService.alarm("cube处理账号删除失败: " + e.getMessage());
        }
    }
}