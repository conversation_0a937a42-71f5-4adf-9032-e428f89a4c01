package com.tal.sea.seaover.application.dto.request.nightLightConfig;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备端编辑夜灯配置请求对象
 */
@Data
public class NightLightConfigEditForDeviceRequest implements Serializable {

    private String talId;

    @NotNull(message = "brightness cannot be empty")
    private Integer brightness;

    @NotNull(message = "autoLight cannot be empty")
    private Integer autoLight;
}
