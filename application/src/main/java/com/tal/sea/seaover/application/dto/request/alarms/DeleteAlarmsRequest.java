package com.tal.sea.seaover.application.dto.request.alarms;

import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
public class DeleteAlarmsRequest implements Serializable {

    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * sn
     */
    private String sn;

    /**
     * alarmId
     */
    @NotNull(message = "alarmId cannot be empty")
    private Long alarmId;

    @NotNull(message = "lastModifiedBy cannot be empty")
    private Integer lastModifiedBy;
    /**
     * 闹钟唯一标识
     */
    private String unionId;
    public void checkParameter(){
        if (Objects.equals(lastModifiedBy, IdentityEnum.PARENT.getValue())) {
            if (StringUtils.isEmpty(sn)) {
                throw new BusinessException("Device sn cannot be empty");
            }
        }
    }
}
