package com.tal.sea.seaover.application.dto.request.alarms;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetAlarmsEnabledForDeviceRequest implements Serializable {
    /**
     * alarmId
     */
    @NotNull(message = "id cannot be empty")
    private Long id;

    /**
     * 是否启用
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;


    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * unionId，用于备用查询
     */
    private String unionId;
}
