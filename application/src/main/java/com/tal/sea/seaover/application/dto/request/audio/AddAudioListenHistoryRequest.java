package com.tal.sea.seaover.application.dto.request.audio;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AddAudioListenHistoryRequest {

    private String talId;

    @NotBlank(message = "Audio unique identifier cannot be blank")
    private String audioUnionId;

    @NotNull(message = "Scene mode cannot be null")
    @Min(value = 1, message = "Scene mode must be 1 or 2")
    private Integer sceneMode;

    @NotNull(message = "Listen time cannot be null")
    private Long listenTime;
}