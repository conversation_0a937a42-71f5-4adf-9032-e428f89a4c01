package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.request.alarms.*;
import com.tal.sea.seaover.application.dto.response.alarms.UserAlarmsResponse;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.useralarms.UserAlarmsService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/inner/datahub/cube/alarm")
public class InnerUserAlarmsController {
    @Autowired
    private UserAlarmsService userAlarmsService;
    @Resource
    private AlarmService alarmService;

    /**
     * 新增用户闹钟
     */
    @PostMapping("/add")
    public ResponseEntity addAlarm(@RequestBody @Validated AlarmsAddRequest alarmsAddRequest) {
        try {
            UserAlarmsResponse alarmsResponse = userAlarmsService.addAlarm(alarmsAddRequest);
            return ResponseUtil.successWithData(alarmsResponse);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("add addAlarm fail! exception:{}", e.getMessage());
            alarmService.alarm("add addAlarm fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.ADD_ALARM_FAIL.getMsg());
        }
    }


    /**
     * 获取用户闹钟
     */
    @PostMapping("/get")
    public ResponseEntity<UserAlarmsResponse> getAlarm(@RequestBody @Validated GetAlarmsRequest getAlarmsRequest) {
        UserAlarmsResponse alarmsResponse = userAlarmsService.getUserAlarmResponse(getAlarmsRequest.getAlarmId());
        return ResponseUtil.successWithData(alarmsResponse);
    }


    /**
     * 获取用户开启闹钟数量
     */
    @PostMapping("/getOpenCount")
    public ResponseEntity<Integer> getOpenCount(@RequestBody @Validated GetOpenCountRequest getOpenCountRequest) {
        Integer result = userAlarmsService.getOpenCount(getOpenCountRequest);
        return ResponseUtil.successWithData(result);
    }

    /**
     * 获取用户闹钟列表
     */
    @PostMapping("/list")
    public ResponseEntity<List<UserAlarmsResponse>> alarmsList(@RequestBody @Validated AlarmListRequest alarmListRequest) {
        List<UserAlarmsResponse> result = userAlarmsService.alarmsList(alarmListRequest);
        return ResponseUtil.successWithData(result);
    }

    /**
     * 设置闹钟状态
     */
    @PostMapping("/setEnabled")
    public ResponseEntity setEnabled(@RequestBody @Validated SetAlarmsEnabledRequest setAlarmsEnabledRequest) {
        try {
            UserAlarms result = userAlarmsService.setEnabled(setAlarmsEnabledRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            alarmService.alarm("set alarms enabled fail! reason:" + e.getMessage());
            log.error("set alarms enabled fail! exception:{}", e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.SET_ALARM_ENABLED_FAIL.getMsg());
        }
    }


    /**
     * 编辑闹钟状态
     */
    @PostMapping("/edit")
    public ResponseEntity edit(@RequestBody @Validated AlarmsEditRequest alarmsEditRequest) {
        try {
            UserAlarms result = userAlarmsService.edit(alarmsEditRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit alarms  fail! exception:{}", e.getMessage());
            alarmService.alarm("edit alarms  fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.EDIT_ALARM_FAIL.getMsg());
        }
    }


    /**
     * 删除用户闹钟
     */
    @PostMapping("/delete")
    public ResponseEntity deleteAlarm(@RequestBody @Validated DeleteAlarmsRequest deleteAlarmsRequest) {
        try {
            UserAlarms result = userAlarmsService.deleteAlarm(deleteAlarmsRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            alarmService.alarm("delete alarms  fail! reason:" + e.getMessage());
            log.error("delete alarms  fail! exception:{}", e.getMessage());
            return ResponseUtil.failWith500("delete alarms fail!");
        }
    }

    /**
     * 批量查询闹钟
     */
    @PostMapping("/batchListByUuIds")
    public ResponseEntity<List<UserAlarmsResponse>> batchListByUuIds(@RequestBody @Validated AlarmsBatchListRequest request) {
        try {
            List<UserAlarmsResponse> result = userAlarmsService.batchListByUuids(request.getUuids());
            return ResponseUtil.successWithData(result);
        } catch (Exception e) {
            log.error("batch list alarms fail! exception:{}", e.getMessage());
            alarmService.alarm("batch list alarms fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500("batch list alarms fail!");
        }
    }

}
