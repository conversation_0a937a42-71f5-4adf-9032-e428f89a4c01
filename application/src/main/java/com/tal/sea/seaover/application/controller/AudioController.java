package com.tal.sea.seaover.application.controller;

import cn.hutool.json.JSONUtil;
import com.tal.sea.seaover.application.dto.request.audio.AddAudioRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioListRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioListV2Request;
import com.tal.sea.seaover.application.dto.request.audio.DeleteAudioRequest;
import com.tal.sea.seaover.application.dto.response.audio.*;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.audio.AudioCacheService;
import com.tal.sea.seaover.application.service.audio.AudioService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 音频Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/audio")
public class AudioController {

    @Autowired
    private AudioService audioService;

    @Autowired
    private AudioCacheService audioCacheService;

    @PostMapping("/add")
    public ResponseEntity add(@Valid @RequestBody AddAudioRequest request) {
        audioService.add(request);
        return ResponseUtil.successWithoutData();
    }

    @PostMapping("/delete")
    public ResponseEntity delete(@Valid @RequestBody DeleteAudioRequest request) {
        try {
            audioService.delete(request);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        }
        return ResponseUtil.successWithoutData();
    }

    @PostMapping("/list")
    public ResponseEntity<List<AudioListResponse>> list(@RequestHeader("X-Tal-Id") String talId, @Valid @RequestBody AudioListRequest request) {
        request.setTalId(talId);
        List<AudioListResponse> audioList = audioService.list(request);
        log.info("query audio list result:{}", audioList);
        return ResponseUtil.successWithData(audioList);
    }

    @GetMapping("/refresh_cache")
    public ResponseEntity refreshCache() {
        audioCacheService.refreshAudioCache();
        return ResponseUtil.successWithoutData();
    }

    @GetMapping("/album/list")
    public ResponseEntity<AudioAlbumResponse> getAlbumList(@RequestHeader("X-Tal-Id") String talId) {
        return ResponseUtil.successWithData(audioService.getAlbumList(talId));
    }

    @GetMapping("/options")
    public ResponseEntity<AudioOptionsResponse> getAudioOptions(@RequestHeader("X-Tal-Id") String talId,@RequestParam(value = "sceneMode",required = false)Integer sceneMode) {
        return ResponseUtil.successWithData(audioService.getAudioOptions(talId,sceneMode));
    }

    @PostMapping("/list/v2")
    public ResponseEntity<AudioListV2Response> getAudioListV2(@RequestHeader("X-Tal-Id") String talId, @Valid @RequestBody AudioListV2Request request) {
        request.setTalId(talId);
        log.info("【获取播放列表】AudioController.getAudioListV2 param:{}", JSONUtil.toJsonStr(request));
        return ResponseUtil.successWithData(audioService.getAudioListV2(request));
    }

    /**
     * 内存重新加载
     */
    @GetMapping("/memory/reload")
    public ResponseEntity<Boolean> reloadAudioList(){
        return ResponseUtil.successWithData(audioService.reloadAudioList());
    }


}