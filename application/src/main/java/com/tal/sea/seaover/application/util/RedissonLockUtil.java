package com.tal.sea.seaover.application.util;


import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class RedissonLockUtil {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 执行带分布式锁的业务逻辑
     *
     * @param lockKey 锁的键
     * @param action  业务逻辑
     * @param <T>     返回值类型
     * @return 业务逻辑的结果
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> action) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(20, TimeUnit.SECONDS);
            return action.get();
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("Lock released for key: {}", lockKey);
            }
        }
    }
}
