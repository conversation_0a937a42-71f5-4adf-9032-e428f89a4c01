package com.tal.sea.seaover.application;

import com.alibaba.cloud.nacos.registry.NacosRegistration;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.serviceregistry.ServiceRegistry;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@EnableAsync
@EnableFeignClients
@SpringBootApplication
@EnableConfigurationProperties
@RestController
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    /**
     * 健康检查-就绪检查：readinessProbe 检查不通过时k8s将从LB上摘除
     * k8s会检查接口的返回是否为200，返回200则代表接口正常
     */
    @GetMapping(value = "/inner/health/available")
    public ResponseEntity available() throws Exception {
        //业务检查
        return ResponseUtil.successWithoutData();
    }

    /**
     * 探活接口
     *
     * @return
     */
    @RequestMapping("/inner/health/alive")
    public ResponseEntity alive() {
        return ResponseUtil.successWithoutData();
    }

    @Resource
    private NacosRegistration registration;
    @Resource
    private ServiceRegistry serviceRegistry;

    /**
     * 配置容器停止前的处理逻辑
     * 每个微服务暴露出来接口/inner/discover/deRegister
     * 进行将自己从注册中心（nacos）摘除
     *
     * @return
     */
    @GetMapping("/inner/discovery/unRegister")
    public ResponseEntity<String> deregister() {
        try {
            serviceRegistry.deregister(registration);
        } catch (Exception e) {
            return ResponseUtil.failWith500("deregister fail");
        }
        return ResponseUtil.successWithoutData();
    }
}
