package com.tal.sea.seaover.application.service.userlogout;

import com.tal.sea.seaover.application.service.nightLightConfig.NightLightConfigBatchService;
import com.tal.sea.seaover.application.service.pets.PetsBatchService;
import com.tal.sea.seaover.application.service.useralarms.UserAlarmsBatchService;
import com.tal.sea.seaover.application.service.userschedule.UserScheduleBatchService;
import com.tal.sea.seaover.application.util.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 用户登出处理服务
 * 负责处理用户登出时的数据清理工作
 */
@Slf4j
@Service
public class UserLogoutService {

    @Autowired
    private AudioListenHistoryBatchService audioListenHistoryBatchService;

    @Autowired
    private NightLightConfigBatchService nightLightConfigBatchService;

    @Autowired
    private PetsBatchService petsBatchService;

    @Autowired
    private UserAlarmsBatchService userAlarmsBatchService;

    @Autowired
    private UserPetHousePartsBatchService userPetHousePartsBatchService;

    @Autowired
    private UserScheduleBatchService userScheduleBatchService;

    /**
     * 处理用户登出数据
     * 对指定的talId列表进行数据处理：
     * 1. 将talId进行两次SHA-256加密
     * 2. 将删除字段设置为1
     * 3. 更新时间
     *
     * @param talIdList 用户ID列表
     */
    @Transactional
    public void processUserLogoutData(List<String> talIdList) {
        if (CollectionUtils.isEmpty(talIdList)) {
            log.warn("TalId list is empty, skip processing user logout data");
            return;
        }

        log.info("Starting to process user logout data for {} users", talIdList.size());

        for (String talId : talIdList) {
            try {
                processUserLogoutDataForSingleUser(talId);
            } catch (Exception e) {
                // 继续处理其他用户，不因为单个用户失败而中断整个流程
                log.error("Failed to process user logout data for talId: {}, error: {}", talId, e.getMessage(), e);
            }
        }

        log.info("Completed processing user logout data for {} users", talIdList.size());
    }

    /**
     * 处理单个用户的登出数据
     *
     * @param talId 用户ID
     */
    @Transactional
    public void processUserLogoutDataForSingleUser(String talId) {
        log.info("Processing user logout data for talId: {}", talId);

        try {
            // 对talId进行两次SHA-256加密
            String encryptedTalId = SHA256Util.encryptTwice(talId);
            // 处理音频收听历史数据
            audioListenHistoryBatchService.processUserLogoutData(talId, encryptedTalId);

            // 处理夜灯配置数据
            nightLightConfigBatchService.processUserLogoutData(talId, encryptedTalId);

            // 处理用户宠物数据
            petsBatchService.processUserLogoutData(talId, encryptedTalId);

            // 处理用户闹钟数据
            userAlarmsBatchService.processUserLogoutData(talId,encryptedTalId);

            // 处理用户宠物小屋装扮数据
            userPetHousePartsBatchService.processUserLogoutData(talId,encryptedTalId);

            // 处理用户日程数据
            userScheduleBatchService.processUserLogoutData(talId,encryptedTalId);

            log.info("Successfully processed user logout data for talId: {}", talId);

        } catch (Exception e) {
            log.error("Error processing user logout data for talId: {}, error: {}", talId, e.getMessage(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }
}
