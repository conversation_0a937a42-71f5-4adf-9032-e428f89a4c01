package com.tal.sea.seaover.application.service.useralarms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.constant.RedisConstant;
import com.tal.sea.seaover.application.dto.request.alarms.*;
import com.tal.sea.seaover.application.dto.response.alarms.UserAlarmsResponse;
import com.tal.sea.seaover.application.dto.response.alarms.UserAlarmsSyncDataResponse;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.DeleteStatusEnum;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.mapper.UserAlarmsMapper;
import com.tal.sea.seaover.application.service.iot.IotService;
import com.tal.sea.seaover.application.service.resources.CubeResourcesService;
import com.tal.sea.seaover.application.util.DateUtils;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserAlarmsService {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private UserAlarmsMapper userAlarmsMapper;
    @Autowired
    private IotService iotService;
    @Autowired
    private CubeResourcesService cubeResourcesService;

    /**
     * 新增闹钟
     *
     * @param requestParameter 请求参数
     * @return 闹钟
     * @throws Exception 异常
     */
    public UserAlarmsResponse addAlarm(AlarmsAddRequest requestParameter) {
        log.info("addAlarm requestParameter: {}", GsonUtil.toJson(requestParameter));
        RLock lock = null;
        //参数校验
        requestParameter.checkParameter();
        try {
            String talId = requestParameter.getTalId();
            //获取闹钟用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_ALARM_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            //判断unionId是否存在
            if (existsByUnionId(requestParameter.getUnionId(), talId)) {
                throw new BusinessException(ErrorEnum.UNION_ID_EXISTS);
            }
            //判断闹钟的数量是否超出100
            Integer notDeleteCount = getNotDeleteCount(talId);
            if (notDeleteCount > 100) {
                throw new BusinessException(ErrorEnum.ALARM_LIMIT_REACHED);
            }
            //如果是一次性闹钟默认将alarmDay对应的周几放入到repeatDays
            if (requestParameter.getRepeating() == YesNoEnum.NO.getValue()) {
                String alarmDay = requestParameter.getAlarmDay();
                if (StringUtils.isNotEmpty(alarmDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(alarmDay);
                    requestParameter.setRepeatDays(dayOfWeek);
                }
            }
            //查询出用户的闹钟列表(未删除的)
            List<UserAlarms> userAlarms = queryNonDeletedAlarms(requestParameter.getTalId(), requestParameter.getAlarmTime());
            //判断时间是否冲突
            boolean isTimeConflict = checkAlarmConflict(requestParameter.getRepeating(), requestParameter.getAlarmDay(), requestParameter.getRepeatDays(), userAlarms);
            if (isTimeConflict) {
                throw new BusinessException(ErrorEnum.TIME_CONFLICT);
            }
            //新增闹钟
            UserAlarms newUserAlarms = insertUserAlarms(requestParameter);
            UserAlarmsResponse userAlarmsResponse = new UserAlarmsResponse();
            BeanUtils.copyProperties(newUserAlarms, userAlarmsResponse);
            //只有修改者是家长端才调用iot
            if (Objects.equals(requestParameter.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                iotService.iotForAlarms(requestParameter.getSn(), newUserAlarms);
            }
            log.info("addAlarm response: {}", GsonUtil.toJson(userAlarmsResponse));
            return userAlarmsResponse;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


    private UserAlarms insertUserAlarms(AlarmsAddRequest requestParameter) {
        UserAlarms userAlarm = new UserAlarms();
        BeanUtils.copyProperties(requestParameter, userAlarm);
        userAlarm.setCreateTime(new Date());
        userAlarm.setUpdateTime(new Date());
        userAlarm.setDeleted(DeleteStatusEnum.NO_DELETE.getValue());
        userAlarm.setVersion(1);
        userAlarm.setPreSetType(Objects.isNull(requestParameter.getPreSetType()) ? 0 : requestParameter.getPreSetType());
        Integer lastModifiedBy = requestParameter.getLastModifiedBy();
        userAlarm.setLastModifiedBy(lastModifiedBy);
        userAlarmsMapper.insert(userAlarm);
        return userAlarm;
    }

    /**
     * 比对输入的UserAlarms与查询的闹钟集合是否存在冲突
     *
     * @param repeating       是否循环闹钟
     * @param oneOffAlarmDay  一次性闹钟日期
     * @param inputDaysOfWeek 闹钟对应的重复星期
     * @param existingAlarms  查询出的闹钟集合
     * @return true表示有冲突，false表示无冲突
     */
    public boolean checkAlarmConflict(Integer repeating, String oneOffAlarmDay, String inputDaysOfWeek, List<UserAlarms> existingAlarms) {
        // 如果查询结果为空，无冲突
        if (existingAlarms == null || existingAlarms.isEmpty()) {
            return false;
        }
        //如果是循环闹钟
        if (Objects.equals(repeating, YesNoEnum.YES.getValue())) {
            Set<String> inputDaysSet = new HashSet<>(Arrays.asList(inputDaysOfWeek.split(",")));
            // 与闹钟比较：检查周几是否相同
            for (UserAlarms alarm : existingAlarms) {
                //排除掉一次性中未启用的闹钟
                if (alarm.getEnabled() == YesNoEnum.NO.getValue() &&
                        alarm.getRepeating() == YesNoEnum.NO.getValue()) {
                    continue;
                }
                String existingRepeatDays = alarm.getRepeatDays();
                if (StringUtils.isNotEmpty(existingRepeatDays)) {
                    Set<String> existingDaysSet = new HashSet<>(Arrays.asList(existingRepeatDays.split(",")));
                    if (inputDaysSet.stream().anyMatch(existingDaysSet::contains)) {
                        log.info("循环闹钟与库中的星期有冲突, time:{} userAlarmId:{} inputRepeatDay:{} serverRepeatDay:{}",
                                alarm.getAlarmTime(), alarm.getId(), inputDaysSet, existingRepeatDays);
                        return true;
                    }
                }
            }
        } else {
            // 分组：A 【一次性闹钟】 B组（重复性闹钟）
            List<UserAlarms> oneOffAlarms = existingAlarms.stream()
                    .filter(alarm -> alarm.getEnabled() == YesNoEnum.YES.getValue() &&
                            alarm.getRepeating() == YesNoEnum.NO.getValue()).toList();
            List<UserAlarms> repeatingAlarms = existingAlarms.stream()
                    .filter(alarm -> alarm.getRepeating() == YesNoEnum.YES.getValue()).toList();

            // 与A组【一次性闹钟】比对：检查alarmDay是否重复
            if (!oneOffAlarms.isEmpty()) {
                if (StringUtils.isNotEmpty(oneOffAlarmDay)) {
                    for (UserAlarms alarm : oneOffAlarms) {
                        if (oneOffAlarmDay.equals(alarm.getAlarmDay())) {
                            log.info("和库中的一次性闹钟比较日期冲突, time:{} userAlarmId:{} inputRepeatDay:{} serverRepeatDay:{}",
                                    alarm.getAlarmTime(), alarm.getId(), oneOffAlarmDay, alarm.getAlarmDay());
                            return true; // alarmDay重复，存在冲突
                        }
                    }
                }
            }

            // 与B组（重复性闹钟）比对：检查repeatDays是否有交集
            if (!repeatingAlarms.isEmpty()) {
                if (StringUtils.isNotEmpty(inputDaysOfWeek)) {
                    Set<String> inputDaysSet = new HashSet<>(Arrays.asList(inputDaysOfWeek.split(",")));
                    for (UserAlarms alarm : repeatingAlarms) {
                        String existingRepeatDays = alarm.getRepeatDays();
                        if (StringUtils.isNotEmpty(existingRepeatDays)) {
                            Set<String> existingDaysSet = new HashSet<>(Arrays.asList(existingRepeatDays.split(",")));
                            // 检查是否有交集
                            if (inputDaysSet.stream().anyMatch(existingDaysSet::contains)) {
                                log.info("和库中的重复性闹钟比较星期冲突 time:{} userAlarmId:{} inputRepeatDay:{} serverRepeatDay:{}",
                                        alarm.getAlarmTime(), alarm.getId(), inputDaysSet, existingRepeatDays);
                                return true; // repeatDays有交集，存在冲突
                            }
                        }
                    }
                }
            }

        }
        // 没有冲突
        return false;
    }


    /**
     * 查询当前用户未删除且与输入alarmTime匹配的闹钟集合
     *
     * @param talId     talId
     * @param alarmTime 闹钟时间
     * @return 未删除且alarmTime匹配的闹钟列表
     */
    public List<UserAlarms> queryNonDeletedAlarms(String talId, Integer alarmTime) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getAlarmTime, alarmTime)
                .eq(UserAlarms::getTalId, talId)
                .eq(UserAlarms::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return userAlarmsMapper.selectList(queryWrapper);
    }

    /**
     * 查询当前用户未删除、与输入 alarmTime 匹配且排除指定 alarmId 的闹钟集合
     *
     * @param talId     用户 talId
     * @param alarmTime 闹钟时间
     * @param alarmId   要排除的闹钟 ID
     * @return 未删除、alarmTime 匹配且不包含指定 alarmId 的闹钟列表
     */
    public List<UserAlarms> queryNonDeletedAlarmsExcludingId(String talId, Integer alarmTime, Long alarmId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getAlarmTime, alarmTime)
                .eq(UserAlarms::getTalId, talId)
                .eq(UserAlarms::getDeleted, YesNoEnum.NO.getValue())
                .ne(UserAlarms::getId, alarmId);
        return userAlarmsMapper.selectList(queryWrapper);
    }


    /**
     * 根据alarmId查询闹钟
     *
     * @param userAlarmsId 参数
     * @return 闹钟实例
     */
    public UserAlarms getAlarm(Long userAlarmsId) {
        return userAlarmsMapper.selectById(userAlarmsId);
    }

    /**
     * 根据alarmId查询闹钟
     *
     * @param userAlarmsId 参数
     * @return 闹钟实例
     */
    public UserAlarmsResponse getUserAlarmResponse(Long userAlarmsId) {
        UserAlarms userAlarms = userAlarmsMapper.selectById(userAlarmsId);
        UserAlarmsResponse userAlarmsResponse = new UserAlarmsResponse();
        if (userAlarms == null) {
            return null;
        }
        BeanUtils.copyProperties(userAlarms, userAlarmsResponse);
        userAlarmsResponse.setIconAndRingUrl(cubeResourcesService);
        return userAlarmsResponse;
    }

    /**
     * 查询用户已开启未删除的闹钟数量
     *
     * @param requestParameter 请求参数
     * @return 已开启未删除的闹钟数量
     */
    public Integer getOpenCount(GetOpenCountRequest requestParameter) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, requestParameter.getTalId())
                .eq(UserAlarms::getEnabled, YesNoEnum.YES.getValue())
                .eq(UserAlarms::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return Math.toIntExact(userAlarmsMapper.selectCount(queryWrapper));
    }

    /**
     * 查询用户未删除的闹钟数量
     *
     * @param talId 用户ID
     * @return 已开启未删除的闹钟数量
     */
    public Integer getNotDeleteCount(String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, talId)
                .eq(UserAlarms::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return Math.toIntExact(userAlarmsMapper.selectCount(queryWrapper));
    }


    /**
     * 查询用户闹钟列表
     *
     * @param requestParameter 请求参数
     * @return 闹钟列表
     */
    public List<UserAlarmsResponse> alarmsList(AlarmListRequest requestParameter) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, requestParameter.getTalId())
                .eq(UserAlarms::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        queryWrapper.orderByAsc(UserAlarms::getAlarmTime);
        List<UserAlarms> userAlarms = userAlarmsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userAlarms)) {
            return null;
        }
        return userAlarms.stream().map(userAlarm -> {
            UserAlarmsResponse userAlarmsResponse = new UserAlarmsResponse();
            BeanUtils.copyProperties(userAlarm, userAlarmsResponse);
            userAlarmsResponse.setIconAndRingUrl(cubeResourcesService);
            return userAlarmsResponse;
        }).toList();
    }

    /**
     * 设置用户闹钟是否开启
     *
     * @param requestParameter 请求参数
     * @return 影响行数
     */
    public UserAlarms setEnabled(SetAlarmsEnabledRequest requestParameter) {
        RLock lock = null;
        try {
            log.info("setEnabled requestParameter:{}", GsonUtil.toJson(requestParameter));
            UserAlarms alarm = getAlarm(requestParameter.getAlarmId());
            // 如果按照alarmId查询闹钟不存在，就按照unionId查询
            if (Objects.isNull(alarm) && StringUtils.isNotEmpty(requestParameter.getUnionId())) {
                log.info("alarmId {} not found, try to find by unionId {}",
                        requestParameter.getAlarmId(), requestParameter.getUnionId());
                alarm = getAlarmByUnionId(requestParameter.getUnionId());
                if (Objects.isNull(alarm)) {
                    log.error("按照unionId {} 也查询不到闹钟", requestParameter.getUnionId());
                    throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
                }
                log.info("通过unionId {} 找到闹钟，alarmId: {}", requestParameter.getUnionId(), alarm.getId());
            }

            if (Objects.isNull(alarm)) {
                throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
            }
            String talId = alarm.getTalId();
            //获取闹钟用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_ALARM_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            if (Objects.equals(alarm.getEnabled(), requestParameter.getEnabled())) {
                log.info("ignor request because repeat set userAlarm enabled");
                return null;
            }

            //操作的是一次性闹钟，并且是要开启这个闹钟，需要进行闹钟冲突检测
            if (Objects.equals(requestParameter.getEnabled(), YesNoEnum.YES.getValue()) &&
                    Objects.equals(alarm.getRepeating(), YesNoEnum.NO.getValue())) {
                String alarmDay = requestParameter.getAlarmDay();
                if (StringUtils.isEmpty(alarmDay)) {
                    throw new BusinessException("开启一次性闹钟alarmDay不能为空");
                }
                List<UserAlarms> userAlarms = queryNonDeletedAlarmsExcludingId(alarm.getTalId(), alarm.getAlarmTime(), alarm.getId());
                boolean checkAlarmConflict = checkAlarmConflict(YesNoEnum.NO.getValue(), alarmDay, DateUtils.calculateDayOfWeek(alarmDay), userAlarms);
                if (checkAlarmConflict) {
                    throw new BusinessException(ErrorEnum.TIME_CONFLICT);
                }
                alarm.setAlarmDay(alarmDay);
                alarm.setRepeatDays(DateUtils.calculateDayOfWeek(alarmDay));
            }

            alarm.setEnabled(requestParameter.getEnabled());
            alarm.setLastModifiedBy(requestParameter.getLastModifiedBy());
            alarm.setUpdateTime(new Date());
            alarm.setVersion(alarm.getVersion() + 1);
            int result = userAlarmsMapper.updateById(alarm);
            //只有家长端修改了闹钟，才调用IOT服务
            if (Objects.equals(requestParameter.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForAlarms(requestParameter.getSn(), alarm);
            }
            log.info("setEnabled result:{}", result);
            return alarm;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    public UserAlarms edit(AlarmsEditRequest alarmsEditRequest) {
        log.info("editAlarm requestParameter:{}", GsonUtil.toJson(alarmsEditRequest));
        RLock lock = null;
        //参数校验
        alarmsEditRequest.checkParameter();
        try {
            String talId = alarmsEditRequest.getTalId();
            //获取闹钟用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_ALARM_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            UserAlarms alarm = getAlarm(alarmsEditRequest.getId());
            // 如果按照alarmId查询闹钟不存在，就按照unionId查询
            if (Objects.isNull(alarm) && StringUtils.isNotEmpty(alarmsEditRequest.getUnionId())) {
                log.info("edit alarm Id {} not found, try to find by unionId {}",
                        alarmsEditRequest.getId(), alarmsEditRequest.getUnionId());
                alarm = getAlarmByUnionId(alarmsEditRequest.getUnionId());
                if (Objects.isNull(alarm)) {
                    log.error("编辑闹钟时按照unionId {} 也查询不到闹钟", alarmsEditRequest.getUnionId());
                    throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
                }
                alarmsEditRequest.setId(alarm.getId());
                log.info("编辑闹钟时通过unionId {} 找到闹钟，alarmId: {}", alarmsEditRequest.getUnionId(), alarm.getId());
            }

            if (Objects.isNull(alarm)) {
                throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
            }


            //如果是一次性闹钟默认将alarmDay对应的周几放入到repeatDays
            if (Objects.equals(alarmsEditRequest.getRepeating(), YesNoEnum.NO.getValue())) {
                String alarmDay = alarmsEditRequest.getAlarmDay();
                if (StringUtils.isNotEmpty(alarmDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(alarmDay);
                    alarmsEditRequest.setRepeatDays(dayOfWeek);
                }
            }
            //查询出用户未删除不包含自身的的闹钟列表
            List<UserAlarms> userAlarms = queryNonDeletedAlarmsExcludingId(alarmsEditRequest.getTalId(), alarmsEditRequest.getAlarmTime(),
                    alarm.getId());
            //判断时间是否冲突
            boolean isTimeConflict = checkAlarmConflict(alarmsEditRequest.getRepeating(), alarmsEditRequest.getAlarmDay(), alarmsEditRequest.getRepeatDays(), userAlarms);
            if (isTimeConflict) {
                throw new BusinessException(ErrorEnum.TIME_CONFLICT);
            }
            updateUserAlarms(alarmsEditRequest, alarm);
            //只有家长端修改了闹钟，才调用IOT服务
            if (Objects.equals(alarmsEditRequest.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForAlarms(alarmsEditRequest.getSn(), alarm);

            }
            log.info("editAlarm result:{}", GsonUtil.toJson(alarm));
            return alarm;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 更新用户闹钟
     *
     * @param alarmsEditRequest 闹钟编辑请求参数
     * @param alarm             闹钟
     */
    private void updateUserAlarms(AlarmsEditRequest alarmsEditRequest, UserAlarms alarm) {
        // 更新闹钟字段
        BeanUtils.copyProperties(alarmsEditRequest, alarm);
        alarm.setUpdateTime(new Date());
        Integer lastModifiedBy = alarmsEditRequest.getLastModifiedBy();
        alarm.setLastModifiedBy(lastModifiedBy);
        // 设置版本号自增
        alarm.setVersion(alarm.getVersion() + 1);
        // 更新数据库
        userAlarmsMapper.updateById(alarm);
    }

    /**
     * 删除闹钟
     *
     * @param deleteAlarmsRequest 闹钟ID
     * @return 受影响行数
     */
    public UserAlarms deleteAlarm(DeleteAlarmsRequest deleteAlarmsRequest) {
        log.info("deleteAlarm requestParameter:{}", GsonUtil.toJson(deleteAlarmsRequest));
        deleteAlarmsRequest.checkParameter();
        RLock lock = null;
        try {
            String talId = deleteAlarmsRequest.getTalId();
            //获取闹钟用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_ALARM_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            UserAlarms alarm = getAlarm(deleteAlarmsRequest.getAlarmId());

            // 如果按照alarmId查询闹钟不存在，就按照unionId查询
            if (Objects.isNull(alarm) && StringUtils.isNotEmpty(deleteAlarmsRequest.getUnionId())) {
                log.info("delete alarm Id {} not found, try to find by unionId {}",
                        deleteAlarmsRequest.getAlarmId(), deleteAlarmsRequest.getUnionId());
                alarm = getAlarmByUnionId(deleteAlarmsRequest.getUnionId());
                if (Objects.isNull(alarm)) {
                    log.error("删除闹钟时按照unionId {} 也查询不到闹钟", deleteAlarmsRequest.getUnionId());
                    throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
                }
                log.info("删除闹钟时通过unionId {} 找到闹钟，alarmId: {}", deleteAlarmsRequest.getUnionId(), alarm.getId());
            }

            if (Objects.isNull(alarm)) {
                throw new BusinessException(ErrorEnum.USER_ALARMS_NOT_FOUND);
            }

            //不允许删除预制闹钟
            if (Objects.equals(alarm.getIsPreSet(), YesNoEnum.YES.getValue())) {
                throw new BusinessException(ErrorEnum.NOT_ALLOWED_DELETE_DATA);
            }

            alarm.setUpdateTime(new Date());
            Integer lastModifiedBy = deleteAlarmsRequest.getLastModifiedBy();
            alarm.setLastModifiedBy(lastModifiedBy);
            alarm.setVersion(alarm.getVersion() + 1);
            Integer deleteStatus = getDeleteStatus(alarm, lastModifiedBy);
            alarm.setDeleted(deleteStatus);
            int result = userAlarmsMapper.updateById(alarm);
            //只有家长端修改了闹钟，才调用IOT服务
            if (Objects.equals(lastModifiedBy, IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForAlarms(deleteAlarmsRequest.getSn(), alarm);
            }
            log.info("deleteAlarm result:{}", result);
            return alarm;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


    private Integer getDeleteStatus(UserAlarms alarm, Integer lastModifiedBy) {
        if (Objects.equals(lastModifiedBy, IdentityEnum.DEVICE.getValue())) {
            return DeleteStatusEnum.DELETE_SYNCED.getValue();
        } else if (Objects.equals(lastModifiedBy, IdentityEnum.PARENT.getValue())) {
            Integer clientId = alarm.getClientId();
            //家长端删除的时候如果clientId为空说明这个数据还未和设备端同步，直接变成3(不参与数据同步)
            if (clientId == null || clientId == 0) {
                return DeleteStatusEnum.DELETE_SYNCED.getValue();
            } else {
                return DeleteStatusEnum.DELETED.getValue();
            }
        }
        return DeleteStatusEnum.DELETED.getValue();
    }


    public List<UserAlarmsSyncDataResponse> listByTalId(String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, talId);
        queryWrapper.ne(UserAlarms::getDeleted, DeleteStatusEnum.DELETE_SYNCED.getValue());
        List<UserAlarms> userAlarms = userAlarmsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userAlarms)) {
            return null;
        }
        return userAlarms.stream().map(alarms -> {
            UserAlarmsSyncDataResponse userAlarmsRes = new UserAlarmsSyncDataResponse();
            BeanUtils.copyProperties(alarms, userAlarmsRes);
            return userAlarmsRes;
        }).collect(Collectors.toList());

    }

    public boolean existsByUnionId(String unionId, String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getUnionId, unionId);
        queryWrapper.eq(UserAlarms::getTalId, talId);
        return userAlarmsMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 根据unionId查询闹钟
     *
     * @param unionId unionId
     * @return 闹钟实例
     */
    public UserAlarms getAlarmByUnionId(String unionId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getUnionId, unionId);
        return userAlarmsMapper.selectOne(queryWrapper);
    }

    /**
     * 判断是否有预制闹钟
     *
     * @param talId 用户ID
     * @return 是否有预制闹钟
     */
    public boolean isExistPreSetAlarms(String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, talId)
                .eq(UserAlarms::getIsPreSet, YesNoEnum.YES.getValue())
                .eq(UserAlarms::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return userAlarmsMapper.selectCount(queryWrapper) > 0;
    }

    public UserAlarms add(UserAlarms userAlarms) {
        if (Objects.isNull(userAlarms)) {
            return null;
        }
        userAlarms.setVersion(1);
        userAlarms.setCreateTime(new Date());
        userAlarms.setUpdateTime(new Date());
        userAlarmsMapper.insert(userAlarms);
        return userAlarms;
    }


    public UserAlarms update(UserAlarms userAlarms) {
        if (Objects.isNull(userAlarms)) {
            return null;
        }
        userAlarms.setUpdateTime(new Date());
        userAlarms.setVersion(userAlarms.getVersion() + 1);
        userAlarmsMapper.updateById(userAlarms);
        return userAlarms;
    }

    public List<UserAlarms> listByUnionIds(String talId, Set<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return null;
        }
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserAlarms::getUnionId, unionIds);
        queryWrapper.eq(UserAlarms::getTalId, talId);
        return userAlarmsMapper.selectList(queryWrapper);
    }


    public List<UserAlarmsSyncDataResponse> listAllByTalId(String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, talId);
        List<UserAlarms> userAlarms = userAlarmsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userAlarms)) {
            return null;
        }
        return userAlarms.stream().map(alarms -> {
            UserAlarmsSyncDataResponse userAlarmsRes = new UserAlarmsSyncDataResponse();
            BeanUtils.copyProperties(alarms, userAlarmsRes);
            return userAlarmsRes;
        }).collect(Collectors.toList());
    }

    /**
     * 根据UUID列表批量查询闹钟
     *
     * @param uuids UUID列表
     * @return 闹钟响应列表
     */
    public List<UserAlarmsResponse> batchListByUuids(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return null;
        }
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserAlarms::getUnionId, uuids);
        List<UserAlarms> userAlarms = userAlarmsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userAlarms)) {
            return null;
        }
        return userAlarms.stream().map(userAlarm -> {
            UserAlarmsResponse userAlarmsResponse = new UserAlarmsResponse();
            BeanUtils.copyProperties(userAlarm, userAlarmsResponse);
            userAlarmsResponse.setIconAndRingUrl(cubeResourcesService);
            return userAlarmsResponse;
        }).toList();
    }
}
