package com.tal.sea.seaover.application.config.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.IOException;

public class HttpServletRequestReplacedFilter implements Filter {

    private String contentTypeForm = MediaType.APPLICATION_FORM_URLENCODED_VALUE;
    private String contentTypeJson = MediaType.APPLICATION_JSON_VALUE;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        HttpServletResponseWrapper responseWrapper = new HttpServletResponseWrapper((HttpServletResponse) response);
        if (request instanceof HttpServletRequest) {
            String method = ((HttpServletRequest) request).getMethod();
            if (RequestMethod.GET.name().equalsIgnoreCase(method)) {
                chain.doFilter(request, responseWrapper);
            } else if (RequestMethod.POST.name().equalsIgnoreCase(method)) {
                String contentType = request.getContentType();
                if (StringUtils.isBlank(contentType)) {
                    chain.doFilter(requestWrapper != null ? requestWrapper : request, responseWrapper);
                } else {
                    if (contentType.equalsIgnoreCase(contentTypeJson) || contentType.contains(contentTypeJson)) {
                        requestWrapper = new HttpServletRequestWrapper((HttpServletRequest) request);
                    } else if (contentType.equalsIgnoreCase(contentTypeForm) || contentType.contains(contentTypeForm)) {
                        requestWrapper = new RepeatParameterRequestWrapper((HttpServletRequest) request);
                    }
                    chain.doFilter(requestWrapper != null ? requestWrapper : request, responseWrapper);
                }
            } else {
                chain.doFilter(request, responseWrapper);
            }
        }
        ServletOutputStream out = response.getOutputStream();
        byte[] respStr = responseWrapper.getResponseData();
        String resp = new String(respStr, Charsets.UTF_8);
        out.write(resp.getBytes(Charsets.UTF_8));
        out.flush();
    }

    @Override
    public void destroy() {

    }

}
