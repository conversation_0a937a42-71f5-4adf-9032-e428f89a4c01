package com.tal.sea.seaover.application.dto.request.schedules;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetScheduleEnabledForDeviceRequest implements Serializable {
    /**
     * scheduleId
     */
    @NotNull(message = "id cannot be empty")
    private Long id;

    /**
     * 是否启用
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * unionId，用于备用查询
     */
    private String unionId;
}
