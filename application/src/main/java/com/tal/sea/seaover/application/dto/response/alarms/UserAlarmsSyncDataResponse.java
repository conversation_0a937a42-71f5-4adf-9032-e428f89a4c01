package com.tal.sea.seaover.application.dto.response.alarms;

import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户闹钟表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserAlarmsSyncDataResponse extends SyncDataParentResponse implements Serializable {

    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * 铃声ID
     */
    private String ringId;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 预置闹钟类型 预制闹钟类型 0-其他 1-wakeUp 2-sleep
     */
    private Integer preSetType;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}