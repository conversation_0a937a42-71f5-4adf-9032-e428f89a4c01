package com.tal.sea.seaover.application.service.userschedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.UserScheduleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class UserScheduleBatchService extends ServiceImpl<UserScheduleMapper, UserSchedule> implements IService<UserSchedule> {
    /**
     * 批量添加
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean saveBatch(List<UserSchedule> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 批量更新
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean updateBatchByIdForDevice(List<UserSchedule> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        entityList.forEach(data -> {
            data.setUpdateTime(new Date());
            data.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            data.setVersion(data.getVersion() + 1);
        });
        return super.updateBatchById(entityList);
    }

    /**
     * 根据talId查询用户日程记录
     *
     * @param talId 用户ID
     * @return 用户日程记录列表
     */
    public List<UserSchedule> getByTalId(String talId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新用户日程记录的talId和删除标记
     *
     * @param entityList     实体列表
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void updateBatchForUserLogout(List<UserSchedule> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("UserSchedule entityList is empty, skip update");
            return;
        }

        Date now = new Date();
        entityList.forEach(entity -> {
            // 设置加密后的talId
            entity.setTalId(encryptedTalId);
            // 设置删除标记为1
            entity.setDeleted(YesNoEnum.YES.getValue());
            // 设置更新时间
            entity.setUpdateTime(now);
        });

        boolean result = super.updateBatchById(entityList);
        log.info("UserSchedule batch update completed, count: {}, result: {}", entityList.size(), result);
    }

    /**
     * 处理用户登出时的用户日程数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing UserSchedule data for user logout, talId: {}", talId);

        // 查询该用户的所有日程记录
        List<UserSchedule> scheduleList = getByTalId(talId);

        if (CollectionUtils.isEmpty(scheduleList)) {
            log.info("No UserSchedule found for talId: {}", talId);
            return;
        }

        // 批量更新
        updateBatchForUserLogout(scheduleList, encryptedTalId);

        log.info("UserSchedule data processing completed for talId: {}, processed count: {}", talId, scheduleList.size());
    }
}
