package com.tal.sea.seaover.application.service.datasync;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataWrapperRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncLockEnum;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.datasync.client.DataSyncClientStrategy;
import com.tal.sea.seaover.application.util.RedissonLockUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataSyncService {

    @Autowired
    private List<DataSyncClientStrategy> dataSyncClientStrategies;

    @Autowired
    private RedissonLockUtil redissonLockUtil;
    @Autowired
    private AlarmService alarmService;

    private Map<DataSyncProcessorEnum, DataSyncClientStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = dataSyncClientStrategies.stream()
                .collect(Collectors.toMap(DataSyncClientStrategy::getType, s -> s));
    }

    /**
     * 数据同步
     *
     * @param syncDataWrapperRequest 数据同步请求
     * @return map<dataType, SyncResult>
     */
    public Map<String, SyncResult<?>> sync(SyncDataWrapperRequest syncDataWrapperRequest) {
        Map<String, SyncResult<?>> results = new HashMap<>();
        String talId = syncDataWrapperRequest.getTalId();
        List<SyncDataRequest> requests = syncDataWrapperRequest.getSyncDatas();
        if (CollectionUtils.isEmpty(requests)) {
            log.info("数据同步请求参数为空(syncDatas)");
            return results;
        }
        //验证type类型的值是否合法
        checkTypeValue(requests, talId);

        for (SyncDataRequest request : requests) {
            Integer type = request.getType();
            // 根据 SyncDataRequest 的 type 确定处理器
            DataSyncProcessorEnum processorEnum = DataSyncProcessorEnum.fromType(type);
            if (processorEnum == null) {
                continue;
            }
            String dataType = processorEnum.getType();
            String lockKey = getLockKey(processorEnum, talId);
            try {
                DataSyncClientStrategy strategy = strategyMap.get(processorEnum);
                if (strategy == null) {
                    log.warn("talId: {} 的 type: {} 无对应策略", talId, processorEnum);
                    continue;
                }
                List<SyncDataParentRequest> datas = request.getDatas();
                if (Objects.isNull(datas)) {
                    log.error("talId: {} 的 type: {} datas数据为空", talId, processorEnum);
                    continue;
                }
                //将头信息的talId赋给datas
                for (SyncDataParentRequest data : datas) {
                    if (!Objects.isNull(data)){
                        data.setTalId(talId);
                    }
                }
                // 直接使用 request.getDatas()
                SyncResult<?> result = redissonLockUtil.executeWithLock(lockKey,
                        () -> strategy.sync(datas, talId)
                );
                results.put(dataType, result);
            } catch (Exception e) {
                log.error("数据同步失败 talId: {}，dataType:{} 错误: {}", talId, dataType, e.getMessage(), e);
                alarmService.alarm("数据同步失败 talId: " + talId + "，dataType:" + dataType + " 错误: " + e.getMessage());
                results.put(dataType, new SyncResult<>());
            }
        }

        return results;
    }

    private void checkTypeValue(List<SyncDataRequest> requests, String talId) {
        requests.forEach(request -> {
            Integer type = request.getType();
            DataSyncProcessorEnum processorEnum =
                    DataSyncProcessorEnum.fromType(type);
            if (processorEnum == null) {
                log.error("talId: {} 的 type: {} 无效", talId, type);
                throw new BusinessException("type的值" + type + "无效!");
            }
        });
    }

    private String getLockKey(DataSyncProcessorEnum processorEnum, String talId) {
        return DataSyncLockEnum.fromType(processorEnum).getLockPrefix() + talId;
    }
}
