package com.tal.sea.seaover.application.enums;

public enum ErrorEnum {
    /**
     * 返回给端的统一错误码
     */
    COMMON_CODE(703000, ""),



    /**
     * 依赖接口调用失败
     */
    INTERFACE_FAIL(703500, "The service is temporarily unavailable. Please try again later."),

    /**
     * 时间冲突错误码
     */
    TIME_CONFLICT(703001, "Time conflict"),

    INVALID_ALARM_DAY_FORMAT(703002, "The date format of the AlarmDay field is incorrect"),
    ADD_ALARM_FAIL(703003, "Add alarm fail"),
    LOCK_ACQUIRE_FAILED(703004, "Get lock fail"),
    USER_ALARMS_NOT_FOUND(703005, " User alarm not found"),
    SET_ALARM_ENABLED_FAIL(703006, "Set user alarms enabled fail"),
    EDIT_ALARM_FAIL(703007, "Edit alarm fail"),
    DELETE_ALARM_FAIL(703008, "Delete alarm fail"),
    NOT_ALLOWED_DELETE_DATA(703008, "Not allowed to delete pre data"),
    ALARM_LIMIT_REACHED(703009, "Alarm limit reached."),

    ADD_SCHEDULE_FAIL(703100, "Add Schedule fail"),
    USER_SCHEDULE_NOT_FOUND(703101, " User schedule not found"),
    SET_SCHEDULE_ENABLED_FAIL(703102, "Set user schedule enabled fail"),
    EDIT_SCHEDULE_FAIL(703103, "Edit schedule fail"),
    DELETE_SCHEDULE_FAIL(703104, "Delete alarm fail"),
    NIGHT_LIGHT_CONFIG_NOT_FOUND(703105, "Night light config not found"),
    UNION_ID_EXISTS(703106, "unionId repeat"),
    SCHEDULE_LIMIT_REACHED(703107, "Schedule limit reached."),


    CONTENT_SAFETY_SEVERITY_NOT_PASS(703201, "content safety severity not pass"),

    IOT_SERVICE_CALL_FAIL(703301, "Iot service call fail"),

    ;


    private int code;
    private String msg;

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    ErrorEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
