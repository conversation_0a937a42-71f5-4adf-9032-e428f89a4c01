package com.tal.sea.seaover.application.dto.response.syncdata;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SyncResult<T> {
    /**
     * 服务端添加成功需要客户端更改本地的ID和version
     */
    private List<T> added;
    /**
     * 服务端有新增需要设备端做本地新增
     */
    private List<T> toAdd;
    /**
     * 服务端有修改需要设备端做本地修改
     */
    private List<T> toUpdate;
    /**
     * 服务端根据设备的数据做了修改，需要客户端更新本地的version
     */
    private List<T> toUpdateVersion;
    /**
     * 和服务端有冲突或者服务端有删除需要设备端做本地删除
     */
    private List<T> toDelete; // 客户端需删除的记录

    public SyncResult() {
        this.added = new ArrayList<>();
        this.toAdd = new ArrayList<>();
        this.toUpdate = new ArrayList<>();
        this.toUpdateVersion = new ArrayList<>();
        this.toDelete = new ArrayList<>();
    }
}

