package com.tal.sea.seaover.application.dto.request.schedules;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetScheduleEnabledRequest implements Serializable {
    /**
     * scheduleId
     */
    @NotNull(message = "scheduleId cannot be empty")
    private Long scheduleId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 是否启用
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * 最后修改人
     */
    @NotNull(message = "lastModifiedBy cannot be empty")
    private Integer lastModifiedBy;

    /**
     * unionId，用于备用查询
     */
    private String unionId;
}