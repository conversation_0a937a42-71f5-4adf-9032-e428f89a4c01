package com.tal.sea.seaover.application.dto.request.schedules;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeleteScheduleRequest implements Serializable {

    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * scheduleId
     */
    @NotNull(message = "scheduleId cannot be empty")
    private Long scheduleId;


    @NotNull(message = "Last modifier cannot be empty")
    private Integer lastModifiedBy;

    /**
     * unionId
     */
    private String unionId;
}
