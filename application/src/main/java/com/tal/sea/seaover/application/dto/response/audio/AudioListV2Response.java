package com.tal.sea.seaover.application.dto.response.audio;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
public class AudioListV2Response {

    private List<AudioDTO> audioList;
    //音频id(非必填)
    private String audioId;
    //年龄段id(非必填)
    private Long ageRangeId;
    //风格类型id(非必填)
    private Long genresId;
    //专辑id(非必填)
    private Long albumId;
    //日间夜间(非必填)
    private Integer sceneMode;
} 