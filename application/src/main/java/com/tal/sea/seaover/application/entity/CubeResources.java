package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 闹钟资源表
 *
 * @TableName tb_cube_resources
 */
@TableName(value = "tb_cube_resources")
@Data
public class CubeResources implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 适用于的业务 1闹钟 2日程
     */
    private Integer busType;

    /**
     * 资源类型 1.icon 2.ring
     */
    private Integer type;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件唯一标识(设备端和家长端保持一致)
     */
    private String fileId;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}