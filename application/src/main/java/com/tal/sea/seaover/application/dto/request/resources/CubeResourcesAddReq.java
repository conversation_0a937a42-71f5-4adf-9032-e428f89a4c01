package com.tal.sea.seaover.application.dto.request.resources;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeResourcesAddReq {

    /**
     * 适用于的业务 1闹钟 2日程
     */
    @NotNull(message = "busType cannot be empty")
    private Integer busType;

    /**
     * 资源类型 1.icon 2.ring
     */
    @NotNull(message = "type cannot be empty")
    private Integer type;

    @NotBlank(message = "name cannot be empty")
    private String name;
    
    /**
     * 文件唯一标识(设备端和家长端保持一致)
     */
    @NotBlank(message = "fileId cannot be empty")
    private String fileId;

    /**
     * 文件地址
     */
    @NotBlank(message = "url cannot be empty")
    private String url;

}
