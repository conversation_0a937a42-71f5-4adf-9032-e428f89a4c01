package com.tal.sea.seaover.application.dto.request.audio;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AudioListRequest {

    @NotNull(message = "Scene mode cannot be null")
    @Min(value = 1, message = "Scene mode must be 1 or 2")
    private Integer sceneMode;

    @NotNull(message = "Page number cannot be null")
    @Min(value = 1, message = "Page number must be greater than 0")
    private Integer pageNumber;

    @NotNull(message = "Pagination direction cannot be null")
    @Min(value = 1, message = "Pagination direction must be 1 or 2")
    private Integer skip;

    private String talId;

    @Valid
    private List<ListenRecord> listenRecord;

    @Data
    public static class ListenRecord {
        @NotNull(message = "Audio identifier cannot be null")
        private String unionId;

        @NotNull(message = "Listen time cannot be null")
        private Long listenTime;
    }
}