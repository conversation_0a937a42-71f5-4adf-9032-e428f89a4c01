package com.tal.sea.seaover.application.service.safety;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.xpod.tools.code.ResponseCodeEnum;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Slf4j
@Service
@ConfigurationProperties(prefix = "safety")
@RefreshScope
public class ContentSafetyService {
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ContentSafetyFeign contentSafetyFeign;

    @Resource
    private AlarmService alarmXtqService;

    /**
     * Hate（仇恨） 1
     * Sexual（性相关）0
     * Self-Harm（自残）1
     * Violence（暴力）1
     */
    private Map<String, Integer> categorySeverityMap;


    /**
     * 对文本进行内容审核
     *
     * @param text 文本内容
     */
    public boolean checkTextContentSafety(String text) {
        if (StringUtils.isEmpty(text)) {
            return true;
        }
        ContentSafetyTextRequest request = new ContentSafetyTextRequest();
        request.setText(text);

        long t1 = System.currentTimeMillis();
        long cost;
        ResponseEntity<List<ContentSafetyResponse>> response;
        try {
            response = contentSafetyFeign.text(request);
            cost = System.currentTimeMillis() - t1;
            log.info("调用内容审核接口耗时:{}ms 返回结果:{}", cost, objectMapper.writeValueAsString(response));
        } catch (Exception e) {
            //添加日志入库
            String errorMsg = "调用内容审核接口出错(601) 错误信息:" + e.getMessage();
            log.error(errorMsg);
            alarmXtqService.alarm(errorMsg);
            throw new BusinessException(ErrorEnum.INTERFACE_FAIL);
        }

        if (!Objects.equals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode())) {
            String errorMsg = "调用内容审核接口报错(601服务)  code:" + response.getCode() + " message:" + response.getMessage();
            log.error(errorMsg);
            alarmXtqService.alarm(errorMsg);
            throw new BusinessException(ErrorEnum.INTERFACE_FAIL);
        }

        List<ContentSafetyResponse> contentSafetyResponseList = response.getData();
        if (CollectionUtils.isEmpty(contentSafetyResponseList)) {
            String errorMsg = "调用内容审核接口报错(601服务) 未返回任何结果 code:" + response.getCode() + " message:" + response.getMessage();
            log.error(errorMsg);
            alarmXtqService.alarm(errorMsg);
            throw new BusinessException(ErrorEnum.INTERFACE_FAIL);
        }
        for (ContentSafetyResponse csr : contentSafetyResponseList) {
            String category = csr.getCategory();
            Integer responseSeverity = csr.getSeverity();
            Integer configSeverity = categorySeverityMap.get(category);
            if (configSeverity != null && responseSeverity > configSeverity) {
                log.error("内容审核未通过  category:{} severity:{} configSeverity:{}", category, responseSeverity, configSeverity);
                return false;
            }
        }
        return true;
    }

}
