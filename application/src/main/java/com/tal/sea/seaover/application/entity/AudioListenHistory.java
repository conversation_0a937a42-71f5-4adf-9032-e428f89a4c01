package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 音频收听历史实体
 *
 * @TableName tb_audio_listen_history
 */
@TableName(value = "tb_audio_listen_history")
@Data
public class AudioListenHistory implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 音频唯一标识
     */
    private String audioUnionId;

    /**
     * 情景模式，1-日间模式，2-哄睡模式
     */
    private Integer sceneMode;

    /**
     * 收听时间
     */
    private Long listenTime;

    /**
     * 专辑ID
     */
    private Long albumId;
    /**
     * 音频内容类型，1-故事，2-音乐，3-播客
     */
    private Integer contentType;
    /**
     * 年龄段id
     */
    private Long ageRangeId;
    /**
     * 类型（1播放列表、2专辑）
     */
    private Integer type;
    /**
     * 播放时长 /秒
     */
    private Long duration;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}