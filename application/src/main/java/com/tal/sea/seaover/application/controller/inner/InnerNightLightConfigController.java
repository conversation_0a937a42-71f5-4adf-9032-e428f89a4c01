package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigEditRequest;
import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigGetRequest;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.nightLightConfig.NightLightConfigService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 夜灯配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/inner/datahub/cube/nightLightConfig")
public class InnerNightLightConfigController {

    @Autowired
    private NightLightConfigService nightLightConfigService;
    @Resource
    private AlarmService alarmService;

    /**
     * 查询夜灯配置
     *
     * @param request 请求参数
     * @return 夜灯配置
     */
    @PostMapping("/get")
    public ResponseEntity getNightLightConfig(@Valid @RequestBody NightLightConfigGetRequest request) {
        request.setLastModifiedBy(IdentityEnum.PARENT.getValue());
        NightLightConfig config = nightLightConfigService.getNightLightConfig(request);
        return ResponseUtil.successWithData(config);
    }

    /**
     * 编辑夜灯配置
     *
     * @param request 请求参数
     * @return 更新后的夜灯配置
     */
    @PostMapping("/edit")
    public ResponseEntity editNightLightConfig(@Valid @RequestBody NightLightConfigEditRequest request) {
        try {
            request.setLastModifiedBy(IdentityEnum.PARENT.getValue());
            //家长端不设置自动开灯
            request.setAutoLight(null);
            NightLightConfig config = nightLightConfigService.editNightLightConfig(request);
            return ResponseUtil.successWithData(config);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit alarms  fail! exception:{}", e.getMessage());
            alarmService.alarm("edit NightLightConfig fail! reason: " + e.getMessage());
            return ResponseUtil.failWith500("edit alarms fail!");
        }
    }

}
