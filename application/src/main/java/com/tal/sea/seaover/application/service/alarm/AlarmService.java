package com.tal.sea.seaover.application.service.alarm;

import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import com.tal.sea.xpod.tools.constant.AppServiceEnum;
import com.xesv5.dog.Alarm;
import com.xesv5.dog.AlarmLevel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class AlarmService {

    @Autowired
    private Alarm xiaotianquanAlarm;

    public void alarm(String content) {

        String profile = System.getProperty("spring.profiles.active");
        if (profile.equals("yyt")) {
            return;
        }

        if (content == null || content.isEmpty()) {
            return;
        }
        StringBuilder prefix = new StringBuilder();
        if (prefix.length() > 0) {
            content = prefix + content;
        }

        Map<String, Object> contentmap = new HashMap<String, Object>();
        contentmap.put("app", "cube-703-datahub");
        contentmap.put("content", content);
        contentmap.put("traceId", ThreadMdcUtil.getTraceId());
        String env = System.getProperty("spring.profiles.active");
        if (StringUtils.isBlank(env)) {
            env = System.getenv("spring.profiles.active");
        }
        contentmap.put("env", env);

        try {
            xiaotianquanAlarm.report(contentmap, AlarmLevel.ERROR, String.valueOf(System.currentTimeMillis() / 1000));
        } catch (Exception e) {
            log.error("Failed to send alarm to xiaotianquan, content:{} , error: {}", content, e.getMessage(), e);
        }

    }

}
