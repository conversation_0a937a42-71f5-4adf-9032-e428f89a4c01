package com.tal.sea.seaover.application.dto.request.audio;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
public class AudioListV2Request {

    //音频id(非必填)
    private String audioId;
    //年龄段id(非必填)
    private Long ageRangeId;
    //风格类型id(非必填)
    private Long genresId;
    //专辑id(非必填)
    private Long albumId;
    //日间夜间(非必填)
    private Integer sceneMode;
    //动作 1下一首 2上一首
    private Integer action;
    //每页数量
    @NotNull(message = "Page number cannot be null")
    @Min(value = 1, message = "Page number must be greater than 0")
    private Integer pageSize;
    private String talId;
}