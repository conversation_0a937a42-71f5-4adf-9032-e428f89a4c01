package com.tal.sea.seaover.application.dto.response.nightLightConfig;

import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class NightLightConfigSyncDataResponse extends SyncDataParentResponse implements Serializable {

    /**
     * 亮度
     */
    private Integer brightness;

    /**
     * 灯效 1常亮 2闪烁效果 3呼吸效果
     */
    private Integer lightingEffects;

    /**
     * 自动照明，0-关闭，1-开启
     */
    private Integer autoLight;

    /**
     * 灯光颜色
     */
    private String color;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

}
