package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户日程表
 *
 * @TableName tb_user_schedule
 */
@TableName(value = "tb_user_schedule")
@Data
public class UserSchedule implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一UUID
     */
    private String unionId;

    /**
     * 此数据对应的设备端中维护的ID值
     */
    private Integer clientId;

    /**
     * 日程名称
     */
    private String name;

    /**
     * 日程时间 24小时制的秒数
     */
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    private Integer notifyDuration;

    /**
     * 重复日 1-7
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * label颜色
     */
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 是否预置日程 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 预置日程类型
     */
    private Integer preSetType;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}