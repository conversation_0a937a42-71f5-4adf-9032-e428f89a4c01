package com.tal.sea.seaover.application.service.datasync.client;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;

import java.util.List;

public interface DataSyncClientStrategy {
    DataSyncProcessorEnum getType();

    SyncResult<?> sync(List<SyncDataParentRequest> requests, String talId);
}
