package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 宠物相册背景实体
 */
@Data
@TableName(value = "tb_pet_album_background")
public class PetAlbumBackground implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 宠物ID
     */
    private Integer petId;

    /**
     * 背景id编号
     */
    private String pbgId;

    /**
     * 解锁等级
     */
    private Integer level;

    /**
     * 缩略图资源
     */
    private String thumbnailUrl;

    /**
     * 大图资源
     */
    private String fullImageUrl;

    /**
     * 排列顺序
     */
    private Integer sort;

    /**
     * app version
     */
    private Long appVersion;

    /**
     * 逻辑删除字段
     */
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}