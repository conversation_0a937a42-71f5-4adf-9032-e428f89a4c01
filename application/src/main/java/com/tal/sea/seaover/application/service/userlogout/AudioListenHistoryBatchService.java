package com.tal.sea.seaover.application.service.userlogout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.AudioListenHistory;
import com.tal.sea.seaover.application.mapper.AudioListenHistoryMapper;
import com.tal.sea.seaover.application.util.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 音频收听历史批量更新服务
 */
@Slf4j
@Service
public class AudioListenHistoryBatchService extends ServiceImpl<AudioListenHistoryMapper, AudioListenHistory> implements IService<AudioListenHistory> {

    /**
     * 根据talId查询音频收听历史记录
     *
     * @param talId 用户ID
     * @return 音频收听历史记录列表
     */
    public List<AudioListenHistory> getByTalId(String talId) {
        LambdaQueryWrapper<AudioListenHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AudioListenHistory::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新音频收听历史记录的talId和删除标记
     *
     * @param entityList     实体列表
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void updateBatchForUserLogout(List<AudioListenHistory> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("AudioListenHistory entityList is empty, skip update");
            return;
        }

        Date now = new Date();
        entityList.forEach(entity -> {
            // 设置加密后的talId
            entity.setTalId(encryptedTalId);
            // 设置删除标记为1
            entity.setDelFlag(1);
            // 设置更新时间
            entity.setUpdateTime(now);
        });

        boolean result = super.updateBatchById(entityList);
        log.info("AudioListenHistory batch update completed, count: {}, result: {}", entityList.size(), result);
    }

    /**
     * 处理用户登出时的音频收听历史数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing AudioListenHistory data for user logout, talId: {}", talId);
        
        // 查询该用户的所有音频收听历史记录
        List<AudioListenHistory> historyList = getByTalId(talId);
        
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("No AudioListenHistory found for talId: {}", talId);
            return;
        }
        // 批量更新
        updateBatchForUserLogout(historyList, encryptedTalId);
        
        log.info("AudioListenHistory data processing completed for talId: {}, processed count: {}", talId, historyList.size());
    }
}
