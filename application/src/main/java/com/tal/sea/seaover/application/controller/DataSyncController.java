package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataWrapperRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.datasync.DataSyncService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.GsonUtil;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/data")
public class DataSyncController {
    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 数据同步
     */
    @PostMapping("/sync")
    public ResponseEntity sync(@RequestHeader("X-Tal-Id") String talId, @Valid @RequestBody SyncDataWrapperRequest request) {
        try {
            //从头信息获取talId sn
            request.setTalId(talId);
            log.info("cube sync  data request:{}", GsonUtil.toJson(request));
            Map<String, SyncResult<?>> syncResult = dataSyncService.sync(request);
            log.info("cube sync  data response:{}", GsonUtil.toJson(syncResult));
            return ResponseUtil.successWithData(syncResult);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("cube sync  data fail! exception:{}", e.getMessage());
            return ResponseUtil.failWith500("cube sync  data fail!");
        }
    }

}
