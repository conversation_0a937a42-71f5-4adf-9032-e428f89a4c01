package com.tal.sea.seaover.application.service.datasync.client.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.service.datasync.DataSyncProcessor;
import com.tal.sea.seaover.application.service.datasync.SyncDataRequestConverter;
import com.tal.sea.seaover.application.service.datasync.client.DataSyncClientStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NightLightDataSyncClientStrategy implements DataSyncClientStrategy {
    @Autowired
    private SyncDataRequestConverter converter;

    @Autowired
    private DataSyncProcessor<SyncDataRequest.NightLightConfigSyncRequest, ?> processor;

    @Override
    public DataSyncProcessorEnum getType() {
        return DataSyncProcessorEnum.NIGHT_LIGHT;
    }

    @Override
    public SyncResult<?> sync(List<SyncDataParentRequest> requests, String talId) {
        List<SyncDataRequest.NightLightConfigSyncRequest> configs = requests.stream()
                .filter(data -> data instanceof SyncDataRequest.NightLightConfigSyncRequest)
                .map(data -> (SyncDataRequest.NightLightConfigSyncRequest) data)
                .toList();
        List<SyncDataRequest.NightLightConfigSyncRequest> deviceDatas = converter.convertNightLightConfigs(configs);
        return processor.process(talId, deviceDatas);
    }
}
