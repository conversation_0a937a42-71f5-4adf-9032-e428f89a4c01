package com.tal.sea.seaover.application.dto.request.alarms;

import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
public class AlarmsAddRequest implements Serializable {
    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId cannot be empty")
    private String unionId;

    /**
     * 设备端维护的此数据ID值
     */
    private Integer clientId;

    /**
     * 闹钟名称
     */
    @NotBlank(message = "alarmName cannot be empty")
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    @NotNull(message = "Alarm time cannot be empty")
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "Alarm repeat setting cannot be empty")
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * 铃声ID
     */
    @NotBlank(message = "ringId cannot be empty")
    private String ringId;

    /**
     * 是否启用，0-否，1-是
     */
    @NotNull(message = "Enabled setting cannot be empty")
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    @NotNull(message = "Preset alarm setting cannot be empty")
    private Integer isPreSet;

    /**
     * 预制闹钟类型 0-其他 1-wakeUp 2-sleep
     */
    private Integer preSetType;

    /**
     * 最后修改者，1-device，2-parent
     */
    @NotNull(message = "lastModifiedBy cannot be empty")
    private Integer lastModifiedBy;

    public void checkParameter() {
        //一次性闹钟时 alarmDay不能为空
        if (Objects.equals(repeating, YesNoEnum.NO.getValue())
                && StringUtils.isEmpty(alarmDay)) {
            throw new BusinessException("One-time alarm alarmDay cannot be empty");
        }
        if (Objects.equals(repeating, YesNoEnum.YES.getValue())
                && StringUtils.isEmpty(repeatDays)) {
            throw new BusinessException("Repeat alarm repeatDays cannot be empty");
        }
    }

}
