package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.service.userlogout.UserLogoutService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/inner/datahub/cube/user/logout")
public class InnerLogoutUserController {
    @Autowired
    private UserLogoutService userLogoutService;

    /**
     * 用户注销消息消费失败进行手动补偿接口
     *
     */
    @PostMapping("/compensate")
    public ResponseEntity compensate(@RequestBody List<String> talIdList) {
        if (CollectionUtils.isEmpty(talIdList)) {
            log.info("talIdList is empty, skip processing user logout data");
            return ResponseUtil.successWithoutData();
        }
        // 调用UserLogoutService处理用户登出数据
        userLogoutService.processUserLogoutData(talIdList);
        return ResponseUtil.successWithoutData();
    }
}
