package com.tal.sea.seaover.application.dto.response.syncdata;

import lombok.Data;

@Data
public class SyncDataParentResponse {
    /**
     * 数据ID
     */
    private Long id;

    /**
     * 设备端维护的此数据ID值
     */
    private Integer clientId;

    /**
     * 用户talId
     */
    private String talId;
    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

}
