package com.tal.sea.seaover.application.config.interceptor;

import com.alibaba.nacos.api.utils.StringUtils;
import com.tal.sea.seaover.application.config.filter.HttpServletResponseWrapper;
import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;


/**
 * 日志拦截器
 */
@Slf4j
public class LogInterceptor implements HandlerInterceptor {
    public static final ThreadLocal<String> LOG_PREFIX = new ThreadLocal<>();
    private static final String START_TIME = "start_time";
    private static final long MAX_EXECUTE_COST = 3000L;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        request.setAttribute(START_TIME, System.currentTimeMillis());
        String traceId = StringUtils.isEmpty(request.getHeader("X-Tal-TraceId")) ? request.getHeader("traceId")
                : request.getHeader("X-Tal-TraceId");

        if (org.apache.commons.lang3.StringUtils.isBlank(traceId)) {
            traceId = RandomStringUtils.randomNumeric(8);
        }
        ThreadMdcUtil.setTraceId(traceId);
        ThreadMdcUtil.setBizId(request.getHeader("X-Biz-Id"));

        setRpcId(request);
        setSn(request);
        setTalId(request);


        if (!(handler instanceof HandlerMethod)) {
            log.error("request_uri: {} | 找不到对应handler方法", request.getRequestURI());
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return false;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        String method = request.getMethod();
        String requestParam = null;
        if (RequestMethod.POST.name().equalsIgnoreCase(method)) {
            String contentType = request.getContentType();
            if (StringUtils.isBlank(contentType)) {
                contentType = MediaType.APPLICATION_JSON_VALUE;
            }
            if (contentType.equalsIgnoreCase(MediaType.APPLICATION_JSON_VALUE) || contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
                requestParam = getRequestPostStr(request);
            }
        }

        String logPrefix = String.format("request_uri: %s | handler: %s | method: %s | Version: %s| DeviceId: %s " +
                        "| Timestamp: %s |Sn:%s |TraceId:%s |SubAppVer:%s|X-Timezone:%s|talId:%s|talAppVersion:%s",
                request.getRequestURI(),
                handlerMethod.getMethod().getDeclaringClass().getName(),
                handlerMethod.getMethod().getName(),
                request.getHeader("X-Tal-Version"),
                request.getHeader("X-Tal-DeviceId"),
                request.getHeader("X-Tal-Timestamp"),
                request.getHeader("X-Tal-Sn"),
                traceId,
                request.getHeader("X-SubAppVer"),
                request.getHeader("X-Tal-Timezone"),
                request.getHeader("talId"),
                request.getHeader("X-Tal-App-Version")
        );
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setStatus(HttpServletResponse.SC_OK);
        log.info("Request Start | {} | params: {}", logPrefix, requestParam);
        LOG_PREFIX.set(logPrefix);
        return true;
    }

    private void setTalId(HttpServletRequest request) {
        String talId = request.getHeader("talId");
        if (!StringUtils.isEmpty(talId)) {
            ThreadMdcUtil.setTalId(talId);
        }
    }

    private void setSn(HttpServletRequest request) {
        String sn = request.getHeader("X-Tal-Sn");
        if (!StringUtils.isEmpty(sn)) {
            ThreadMdcUtil.setSn(sn);
        }
    }

    private void setRpcId(HttpServletRequest request) {
        String rpcId = request.getHeader("rpcId");
        if (!StringUtils.isEmpty(rpcId)) {
            //自增加1 处理异常
            try {
                Integer rpcIdInt = Integer.parseInt(rpcId);
                rpcIdInt++;
                rpcId = String.valueOf(rpcIdInt);
            } catch (Exception e) {
                rpcId = "1";
            }
            ThreadMdcUtil.setRpcId(rpcId);
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        long endTime = System.currentTimeMillis();
        long executeTime = endTime - (Long) request.getAttribute(START_TIME);
        String logPrefix = LOG_PREFIX.get();
        MDC.put(AppConstant.COST, String.valueOf(executeTime));
        byte[] respStr = ((HttpServletResponseWrapper) response).getResponseData();
        String msg = new String(respStr, Charsets.UTF_8);
        if (executeTime > MAX_EXECUTE_COST) {
            log.error("RequestSlow end | {} | response: {} | costs: {}ms", logPrefix, msg, executeTime);
        } else {
            log.info("Request end | {} | response: {} | costs: {}ms", logPrefix, msg, executeTime);
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }


    private static String getRequestPostStr(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        InputStream is = null;
        BufferedReader br = null;
        try {
            is = request.getInputStream();
            br = new BufferedReader(new InputStreamReader(is, Charset.forName("UTF-8")));
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString();
    }

}
