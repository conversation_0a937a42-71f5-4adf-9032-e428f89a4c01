package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 专辑详情表
 */
@TableName(value = "tb_album_detail")
@Data
public class AlbumDetail {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 音频id
     */
    private String audioId;

    /**
     * 专辑id
     */
    private Long albumId;

    /**
     * 专辑名称
     */
    private String name;

    /**
     * 排序值，值越小越靠前
     */
    private Integer sort;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic(value = "0", delval = "1")
    private Byte delFlag;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;
}
