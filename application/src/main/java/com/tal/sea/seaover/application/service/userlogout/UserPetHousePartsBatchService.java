package com.tal.sea.seaover.application.service.userlogout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.UserPetHouseParts;
import com.tal.sea.seaover.application.mapper.UserPetHousePartsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户宠物小屋装扮批量更新服务
 */
@Slf4j
@Service
public class UserPetHousePartsBatchService extends ServiceImpl<UserPetHousePartsMapper, UserPetHouseParts> implements IService<UserPetHouseParts> {

    /**
     * 根据talId查询用户宠物小屋装扮记录
     *
     * @param talId 用户ID
     * @return 用户宠物小屋装扮记录列表
     */
    public List<UserPetHouseParts> getByTalId(String talId) {
        LambdaQueryWrapper<UserPetHouseParts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPetHouseParts::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新用户宠物小屋装扮记录的talId和删除标记
     *
     * @param entityList     实体列表
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void updateBatchForUserLogout(List<UserPetHouseParts> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("UserPetHouseParts entityList is empty, skip update");
            return;
        }
        LambdaUpdateWrapper<UserPetHouseParts> wrapper = new LambdaUpdateWrapper<>();
        List<Long> ids = entityList.stream().map(UserPetHouseParts::getId).collect(Collectors.toList());
        wrapper.in(UserPetHouseParts::getId, ids)// 批量条件
                .set(UserPetHouseParts::getDelFlag, 1)  // 设置删除标记
                .set(UserPetHouseParts::getTalId, encryptedTalId)
                .set(UserPetHouseParts::getUpdateTime, new Date());  // 更新其他字段

        boolean result = super.update(wrapper);
        log.info("UserPetHouseParts batch update completed, count: {}, result: {}", entityList.size(), result);
    }

    /**
     * 处理用户登出时的用户宠物小屋装扮数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing UserPetHouseParts data for user logout, talId: {}", talId);
        
        // 查询该用户的所有宠物小屋装扮记录
        List<UserPetHouseParts> partsList = getByTalId(talId);
        
        if (CollectionUtils.isEmpty(partsList)) {
            log.info("No UserPetHouseParts found for talId: {}", talId);
            return;
        }
        
        // 批量更新
        updateBatchForUserLogout(partsList, encryptedTalId);
        
        log.info("UserPetHouseParts data processing completed for talId: {}, processed count: {}", talId, partsList.size());
    }
}
