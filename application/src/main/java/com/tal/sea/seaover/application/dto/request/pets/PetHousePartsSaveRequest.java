package com.tal.sea.seaover.application.dto.request.pets;

import com.tal.sea.seaover.application.service.valid.PbgIdExists;
import com.tal.sea.seaover.application.service.valid.ValidJson;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 宠物小屋装扮数据保存请求
 */
@Data
public class PetHousePartsSaveRequest {
    @NotNull(message = "petId cannot be null")
    private Integer petId;

    @NotBlank(message = "pbgId cannot be blank")
    @PbgIdExists(message = "pbgId does not exist in the system")
    private String pbgId;

    @NotBlank(message = "selectProp cannot be blank")
    @ValidJson(message = "selectProp must be a valid JSON structure")
    private String selectProp;

}
