package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户闹钟表
 * @TableName tb_user_alarms
 */
@TableName(value = "tb_user_alarms")
@Data
public class UserAlarms implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一UUID
     */
    private String unionId;

    /**
     * 此数据对应的设备端中的ID值
     */
    private Integer clientId;

    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * 铃声ID
     */
    private String ringId;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 预置闹钟类型 预制闹钟类型 0-其他 1-wakeUp 2-sleep
     */
    private Integer preSetType;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}