package com.tal.sea.seaover.application.service.datasync;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据同步模版类
 */
@Slf4j
public abstract class AbstractCubeDataSyncProcess<R extends SyncDataParentRequest, T extends SyncDataParentResponse> implements DataSyncProcessor<R, T> {


    public final SyncResult<T> process(String talId, List<R> deviceDatas) {
        // 获取服务端数据
        List<T> serverExistData = getServerExistData(talId);
        log.info("获取服务端数据:{}", GsonUtil.toJson(serverExistData));
        SyncStagingData<R> syncStagingData = new SyncStagingData<>(talId, deviceDatas);

        //deviceDatas为设备端提交的数据
        if (CollectionUtils.isEmpty(deviceDatas)) {
            //如果设备端没有提交任何数据,将服务端的数据全部给服务端进行新增
            if (!CollectionUtils.isEmpty(serverExistData)) {
                serverExistData.forEach(data -> {
                    syncStagingData.getDeviceToAdd().add(data.getUnionId());
                });
                log.info("设备端提交的数据为空,默认将服务端数据给设备端做新增");
            }
            // 组装数据
            return assemblyResultData(syncStagingData);
        }


        // 服务端需要新增的数据集合
        List<R> serverNeedAddDatas = new ArrayList<>(deviceDatas.size());
        // 服务端需要修改的数据集合
        List<R> serverNeedEditDatas;

        // 将设备端提交的数据按照ID进行分组
        Map<Boolean, List<R>> groupedData = deviceDatas.stream()
                .collect(Collectors.partitioningBy(data -> data.getId() == null || data.getId() <= 0));

        // id为空或者等于0 可能是设备端新增的数据
        List<R> idNotExistDatas = groupedData.get(true);
        if (!CollectionUtils.isEmpty(idNotExistDatas)) {
            List<T> allServerExistData = getAllServerExistData(talId);
            //将allServerExistData按照unionId进行分组
            Map<String, List<T>> groupedServerExistData = CollectionUtils.isEmpty(allServerExistData) ? new HashMap<>()
                    : allServerExistData.stream().collect(Collectors.groupingBy(T::getUnionId));

            //按照talId查询所有的数据
            for (R data : idNotExistDatas) {
                if (Objects.equals(data.getDeleted(), YesNoEnum.YES.getValue())) {
                    log.info("过滤掉-服务端不存在,但客户端还存在,并且已被客户端删除的数据, data:{}", GsonUtil.toJson(data));
                    continue;
                }
                boolean isAlreadyExist = Objects.nonNull(groupedServerExistData.get(data.getUnionId()));
                if (isAlreadyExist) {
                    // 如果服务端存在该unionId的数据，表示服务端已添加过，客户端需更新version和id
                    syncStagingData.getDeviceAdded().add(data.getUnionId());
                } else {
                    // 表示服务端需要新增
                    serverNeedAddDatas.add(data);
                }
            }
        }


        //获取设备端需要新增的数据【处理服务端存在，但是客户端不存在的场景】
        getDeviceToAdd(deviceDatas, serverExistData, syncStagingData);

        // id不等于0 表示客户端可能做了修改,需同步给服务端
        serverNeedEditDatas = groupedData.get(false);
        //【处理服务端修改场景】
        handleServerEdit(serverExistData, serverNeedEditDatas, syncStagingData);

        //【处理服务端新增场景】
        handleServerAdd(serverNeedAddDatas, serverExistData, syncStagingData);

        //处理服务端数据
        executeOperationDb(syncStagingData);
        // 组装数据
        return assemblyResultData(syncStagingData);
    }

    /**
     * 获取设备端需要新增的数据
     *
     * @param deviceDatas     设备端数据
     * @param serverExistData 服务端数据
     * @param syncStagingData 临时同步数据对象
     */
    private void getDeviceToAdd(List<R> deviceDatas, List<T> serverExistData, SyncStagingData<R> syncStagingData) {
        if (!CollectionUtils.isEmpty(serverExistData)) {
            // 收集设备端数据的 id
            Set<Long> deviceIds = deviceDatas.stream()
                    .map(SyncDataParentRequest::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 找出服务端存在但设备端不存在且未删除的数据
            for (T serverData : serverExistData) {
                if (Objects.equals(serverData.getDeleted(), YesNoEnum.YES.getValue())) {
                    log.info("过滤掉服务端存在客户端不存在,但是服务端已删除的数据, data:{}", GsonUtil.toJson(serverData));
                    continue;
                }
                Long serverId = serverData.getId();
                if (serverId != null && !deviceIds.contains(serverId)) {
                    syncStagingData.getDeviceToAdd().add(serverData.getUnionId());
                }
            }
        }
    }

    private void handleServerEdit(List<T> serverExistData, List<R> editParams, SyncStagingData<R> syncStagingData) {
        if (CollectionUtils.isEmpty(serverExistData)) {
            log.info("服务端数据为空，暂时不进行比对操作!");
            return;
        }
        // 将服务端数据按 id 索引
        Map<Long, T> serverDataMap = serverExistData.stream()
                .filter(data -> data.getId() != null)
                .collect(Collectors.toMap(SyncDataParentResponse::getId, data -> data));

        // 比对 editParams 和 serverExistData【处理修改场景】
        for (R deviceData : editParams) {
            T serverData = serverDataMap.get(deviceData.getId());
            if (serverData == null) {
                //设备端提交的数据未再服务端中查询到，默认认为此数据已经被服务端删除
                syncStagingData.getDeviceToDelete().add(deviceData.getUnionId());
                continue;
            }

            int baseVersion = deviceData.getBaseVersion() != null ? deviceData.getBaseVersion() : 0;
            int serverVersion = serverData.getVersion() != null ? serverData.getVersion() : 0;
            int clientVersion = deviceData.getClientVersion() != null ? deviceData.getClientVersion() : 0;

            // 情况 1：baseVersion < serverVersion，服务端有修改
            if (baseVersion < serverVersion) {
                boolean isServerDelete = serverData.getDeleted() != null &&
                        serverData.getDeleted() == YesNoEnum.YES.getValue();
                if (isServerDelete) {
                    // 服务端已删除，设备端需删除
                    syncStagingData.getDeviceToDelete().add(deviceData.getUnionId());
                    //服务端更新删除状态为：DeleteStatusEnum.DELETE_SYNCED
                    syncStagingData.getServerToDelete().add(deviceData);
                } else {
                    // 服务端未删除，设备端需更新
                    syncStagingData.getDeviceToUpdate().add(deviceData.getUnionId());
                }
            }
            // 情况 2：baseVersion == serverVersion，比较 clientVersion
            else if (baseVersion == serverVersion) {
                // 客户端无修改，忽略
                if (clientVersion == serverVersion) {
                    log.info("客户端和服务端都无修改，忽略 data:{}", GsonUtil.toJson(deviceData));
                }
                // 客户端有修改
                else if (clientVersion > serverVersion) {
                    //客户端有可能删除
                    if (Objects.equals(YesNoEnum.YES.getValue(), deviceData.getDeleted())) {
                        //服务端更新删除状态为：DeleteStatusEnum.DELETE_SYNCED
                        syncStagingData.getServerToDelete().add(deviceData);
                        //让设备端做物理删除
                        syncStagingData.getDeviceToDelete().add(deviceData.getUnionId());
                    } else {
                        boolean hasConflict = checkConflict(serverExistData, deviceData, serverData);
                        if (hasConflict) {
                            // 冲突，以服务端为主，设备端以服务端数据为主去更新
                            syncStagingData.getDeviceToUpdate().add(deviceData.getUnionId());
                        } else {
                            // 无冲突，设备端更新版本，服务端更新数据
                            syncStagingData.getDeviceToUpdateVersion().add(deviceData.getUnionId());
                            syncStagingData.getServerToUpdate().add(deviceData);
                        }
                    }
                }
            } else {
                log.error("客户端baseServers大于服务端的ServerVersion,无法进行比对操作! clientVersion:{}, serverVersion:{}", clientVersion, serverVersion);
            }
        }
    }

    private void handleServerAdd(List<R> serverNeedAddDatas, List<T> serverExistData, SyncStagingData<R> syncStagingData) {
        if (!CollectionUtils.isEmpty(serverNeedAddDatas)) {
            for (R pendingServerToAddData : serverNeedAddDatas) {
                if (Objects.equals(YesNoEnum.YES.getValue(), pendingServerToAddData.getDeleted())) {
                    syncStagingData.getDeviceToDelete().add(pendingServerToAddData.getUnionId());
                    continue;
                }
                boolean hasConflict = checkConflict(serverExistData, pendingServerToAddData, null);
                if (hasConflict) {
                    // 冲突，以服务端为主，设备端删除
                    syncStagingData.getDeviceToDelete().add(pendingServerToAddData.getUnionId());
                } else {
                    // 无冲突，设备端更新版本，服务端新增数据
                    syncStagingData.getDeviceAdded().add(pendingServerToAddData.getUnionId());
                    syncStagingData.getServerToAdd().add(pendingServerToAddData);
                }
            }
        }
    }

    /**
     * 根据talId查询服务端数据
     *
     * @param talId 用户ID
     * @return 服务端数据
     */
    protected abstract List<T> getServerExistData(String talId);

    /**
     * 根据talId查询服务端数据
     *
     * @param talId 用户ID
     * @return 服务端数据
     */
    protected abstract List<T> getAllServerExistData(String talId);

    /**
     * 校验冲突
     *
     * @param serverExistData
     * @param deviceData
     * @param currentServerData
     * @return
     */
    protected abstract boolean checkConflict(List<T> serverExistData, R deviceData, T currentServerData);

    /**
     * 执行数据库操作
     *
     * @param syncStagingData 临时同步数据
     */
    protected abstract void executeOperationDb(SyncStagingData<R> syncStagingData);

    /**
     * 组装结果数据返回
     *
     * @param syncStagingData 临时同步数据
     * @return
     */
    protected abstract SyncResult<T> assemblyResultData(SyncStagingData<R> syncStagingData);
}