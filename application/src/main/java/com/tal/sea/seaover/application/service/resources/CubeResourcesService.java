package com.tal.sea.seaover.application.service.resources;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.dto.request.resources.CubeResourcesAddReq;
import com.tal.sea.seaover.application.entity.CubeResources;
import com.tal.sea.seaover.application.enums.ResourcesBusTypeEnum;
import com.tal.sea.seaover.application.enums.ResourceTypeEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.CubeResourcesMapper;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@RefreshScope
public class CubeResourcesService {
    @Value("${cdn.domain:https://static.thinkbuddycdn.com}")
    private String cdnDomain;

    private static final String RESOURCE_CACHE_KEY = "cube:resources:cache";
    private static final String CACHE_INIT_LOCK = "cube:resources:cache:init:lock";
    private static final String CACHE_INITIALIZED_FLAG = "cube:resources:cache:initialized";

    @Autowired
    private CubeResourcesMapper cubeResourcesMapper;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 初始化 Redis 缓存，在服务启动时加载所有 CubeResources 条目。
     */
    @PostConstruct
    public void initCache() {
        refreshCache();
    }

    /**
     * 手动刷新缓存，重置初始化标志并重新加载数据。
     */
    public void refreshCache() {
        executeWithLock(() -> {
            RMap<String, List<CubeResources>> resourceCache = redissonClient.getMap(RESOURCE_CACHE_KEY);
            redissonClient.getBucket(CACHE_INITIALIZED_FLAG).delete();
            resourceCache.clear();
            List<CubeResources> resources = listResources();
            if (!CollectionUtils.isEmpty(resources)) {
                resourceCache.putAll(groupResources(resources));
            }
            redissonClient.getBucket(CACHE_INITIALIZED_FLAG).set(true);
        });
    }

    /**
     * 执行带分布式锁的操作。
     *
     * @param cacheUpdateLogic 缓存更新逻辑
     */
    private void executeWithLock(Runnable cacheUpdateLogic) {
        RLock lock = redissonClient.getLock(CACHE_INIT_LOCK);
        try {
            if (lock.tryLock(60, TimeUnit.SECONDS)) {
                cacheUpdateLogic.run();
            }
        } catch (InterruptedException e) {
            throw new RuntimeException("缓存操作时获取锁被中断", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 查询未删除的资源。
     *
     * @return 资源列表
     */
    private List<CubeResources> listResources() {
        LambdaQueryWrapper<CubeResources> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CubeResources::getDeleted, YesNoEnum.NO.getValue());
        List<CubeResources> cubeResources = cubeResourcesMapper.selectList(wrapper);
        setUrlCdnDomain(cubeResources);
        return cubeResources;
    }

    private void setUrlCdnDomain(List<CubeResources> cubeResources) {
        if (!CollectionUtils.isEmpty(cubeResources)) {
            for (CubeResources cubeResource : cubeResources) {
                cubeResource.setUrl(cdnDomain + cubeResource.getUrl());
            }
        }
    }

    /**
     * 根据 busType 和 type 生成缓存的复合键。
     *
     * @param busType 业务类型 (如 ResourcesBusTypeEnum.ALARM.getValue(), ResourcesBusTypeEnum.SCHEDULE.getValue())
     * @param type    资源类型 (如 ResourceTypeEnum.ICON.getValue(), ResourceTypeEnum.RING.getValue())
     * @return 复合键字符串
     */
    private String generateKey(Integer busType, Integer type) {
        return busType + "_" + type;
    }

    /**
     * 将资源按 busType 和 type 分组。
     *
     * @param resources 资源列表
     * @return 分组后的映射
     */
    private Map<String, List<CubeResources>> groupResources(List<CubeResources> resources) {
        Map<String, List<CubeResources>> groupedResources = new HashMap<>();
        resources.forEach(resource ->
                groupedResources.computeIfAbsent(generateKey(resource.getBusType(), resource.getType()), k -> new ArrayList<>())
                        .add(resource));
        return groupedResources;
    }

    /**
     * 从 Redis 加载特定业务和资源类型的资源。
     *
     * @param resourceCache 缓存
     * @param result        结果映射
     * @param busType       业务类型
     * @param resourceType  资源类型
     */
    private void loadResourcesByType(RMap<String, List<CubeResources>> resourceCache, Map<Integer, List<CubeResources>> result,
                                     ResourcesBusTypeEnum busType, ResourceTypeEnum resourceType) {
        List<CubeResources> resources = resourceCache.get(generateKey(busType.getValue(), resourceType.getValue()));
        result.put(resourceType.getValue(), resources != null ? resources : List.of());
    }

    /**
     * 从数据库加载特定业务和资源类型的资源。
     *
     * @param tempMap      临时映射
     * @param result       结果映射
     * @param busType      业务类型
     * @param resourceType 资源类型
     */
    private void loadResourcesByTypeFromDb(Map<String, List<CubeResources>> tempMap, Map<Integer, List<CubeResources>> result,
                                           ResourcesBusTypeEnum busType, ResourceTypeEnum resourceType) {
        List<CubeResources> resources = tempMap.get(generateKey(busType.getValue(), resourceType.getValue()));
        result.put(resourceType.getValue(), resources != null ? resources : List.of());
    }

    /**
     * 根据 fileId 查询对应的 URL 地址，先从 Redis 缓存中查找，若不存在则从数据库查询。
     *
     * @param busType      业务类型 (如 ResourcesBusTypeEnum.ALARM, ResourcesBusTypeEnum.SCHEDULE)
     * @param resourceType 资源类型 (如 ResourceTypeEnum.ICON, ResourceTypeEnum.RING)
     * @param fileId       数据库中的唯一键
     * @return 对应的 URL 地址，若未找到则返回 null
     */
    public String getUrlByFileId(ResourcesBusTypeEnum busType, ResourceTypeEnum resourceType, String fileId) {
        if (busType == null || resourceType == null || StringUtils.isEmpty(fileId)) {
            return "";
        }

        // 检查缓存是否初始化并获取缓存
        CacheCheckResult checkResult = getResourceCacheWithCheck();
        RMap<String, List<CubeResources>> resourceCache = checkResult.resourceCache();

        // 从缓存中查找特定 busType 和 resourceType 的资源
        if (checkResult.isInitialized()) {
            String key = generateKey(busType.getValue(), resourceType.getValue());
            List<CubeResources> resources = resourceCache.get(key);
            if (resources != null) {
                for (CubeResources resource : resources) {
                    if (fileId.equals(resource.getFileId())) {
                        return resource.getUrl();
                    }
                }
            }
        }

        // 缓存中未找到，从数据库查询
        CubeResources resource = getCubeResources(busType, resourceType, fileId);
        if (resource == null) {
            return "";
        }
        return cdnDomain + resource.getUrl();
    }

    private CubeResources getCubeResources(ResourcesBusTypeEnum busType, ResourceTypeEnum resourceType, String fileId) {
        LambdaQueryWrapper<CubeResources> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CubeResources::getBusType, busType.getValue())
                .eq(CubeResources::getType, resourceType.getValue())
                .eq(CubeResources::getFileId, fileId);
        return cubeResourcesMapper.selectOne(wrapper);
    }

    /**
     * 检查缓存初始化状态并获取缓存。
     *
     * @return 包含缓存和初始化状态的记录
     */
    private record CacheCheckResult(RMap<String, List<CubeResources>> resourceCache, boolean isInitialized) {
    }

    /**
     * 获取缓存并检查初始化状态。
     *
     * @return 缓存检查结果
     */
    private CacheCheckResult getResourceCacheWithCheck() {
        RMap<String, List<CubeResources>> resourceCache = redissonClient.getMap(RESOURCE_CACHE_KEY);
        boolean isInitialized = redissonClient.getBucket(CACHE_INITIALIZED_FLAG).get() != null;
        return new CacheCheckResult(resourceCache, isInitialized);
    }

    /**
     * 从 Redis 缓存中查询闹钟图标和铃声，若缓存未就绪则从数据库获取。
     *
     * @return 包含资源类型的键值映射 (如 1 代表 icon, 2 代表 ring)
     */
    public Map<Integer, List<CubeResources>> listAlarmsIconAndSound() {
        Map<Integer, List<CubeResources>> result = new HashMap<>();
        CacheCheckResult checkResult = getResourceCacheWithCheck();
        if (checkResult.isInitialized()) {
            loadResourcesByType(checkResult.resourceCache(), result, ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.ICON);
            loadResourcesByType(checkResult.resourceCache(), result, ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.RING);
        } else {
            Map<String, List<CubeResources>> tempMap = getListMap();
            loadResourcesByTypeFromDb(tempMap, result, ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.ICON);
            loadResourcesByTypeFromDb(tempMap, result, ResourcesBusTypeEnum.ALARM, ResourceTypeEnum.RING);
        }
        return result;
    }

    /**
     * 添加资源并更新 Redis 缓存。
     *
     * @param cubeResourcesAddReq 资源添加请求
     * @return 添加的 CubeResources 对象
     */
    public CubeResources addResource(CubeResourcesAddReq cubeResourcesAddReq) {
        CubeResources cubeResources = new CubeResources();
        BeanUtils.copyProperties(cubeResourcesAddReq, cubeResources);
        cubeResources.setDeleted(0);
        cubeResources.setCreateTime(new Date());
        cubeResources.setUpdateTime(new Date());

        int result = cubeResourcesMapper.insert(cubeResources);
        if (result > 0) {
            RMap<String, List<CubeResources>> resourceCache = redissonClient.getMap(RESOURCE_CACHE_KEY);
            String key = generateKey(cubeResources.getBusType(), cubeResources.getType());
            List<CubeResources> resources = resourceCache.get(key);
            if (resources == null) {
                resources = new ArrayList<>();
            }
            resources.add(cubeResources);
            resourceCache.put(key, resources);
            return cubeResources;
        }
        return null;
    }

    /**
     * 获取日程图标
     *
     * @return 包含资源类型的键值映射
     */
    public Map<Integer, List<CubeResources>> listScheduleIcon() {
        Map<Integer, List<CubeResources>> result = new HashMap<>();
        CacheCheckResult checkResult = getResourceCacheWithCheck();
        if (checkResult.isInitialized()) {
            loadResourcesByType(checkResult.resourceCache(), result, ResourcesBusTypeEnum.SCHEDULE, ResourceTypeEnum.ICON);
        } else {
            Map<String, List<CubeResources>> tempMap = getListMap();
            loadResourcesByTypeFromDb(tempMap, result, ResourcesBusTypeEnum.SCHEDULE, ResourceTypeEnum.ICON);
        }
        return result;
    }

    /**
     * 获取所有资源并按键分组。
     *
     * @return 分组后的映射
     */
    private Map<String, List<CubeResources>> getListMap() {
        List<CubeResources> resources = listResources();
        return groupResources(resources);
    }
}