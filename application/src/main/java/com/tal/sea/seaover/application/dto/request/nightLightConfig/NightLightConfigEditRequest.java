package com.tal.sea.seaover.application.dto.request.nightLightConfig;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 编辑夜灯配置请求
 */
@Data
public class NightLightConfigEditRequest implements Serializable {
    @NotNull(message = "id cannot be empty")
    private Long id;
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    @NotBlank(message = "sn cannot be empty")
    private String sn;
    @NotNull(message = "brightness cannot be empty")
    private Integer brightness;
    @NotNull(message = "lightingEffects cannot be empty")
    private Integer lightingEffects;
    @NotBlank(message = "color cannot be empty")
    private String color;
    
    private Integer autoLight;
    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;
}
