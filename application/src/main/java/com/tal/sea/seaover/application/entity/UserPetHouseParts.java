package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户宠物小屋装扮实体
 */
@Data
@TableName(value = "tb_user_pet_house_parts")
public class UserPetHouseParts implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private String talId;

    /**
     * 宠物ID
     */
    private Integer petId;

    /**
     * pbgId
     */
    private String pbgId;

    /**
     * json 格式：保存后的装扮组件
     */
    private String selectProp;

    /**
     * 逻辑删除字段
     */
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}