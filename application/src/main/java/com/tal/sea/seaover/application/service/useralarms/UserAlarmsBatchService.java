package com.tal.sea.seaover.application.service.useralarms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.mapper.UserAlarmsMapper;
import com.tal.sea.seaover.application.util.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class UserAlarmsBatchService extends ServiceImpl<UserAlarmsMapper, UserAlarms> implements IService<UserAlarms> {
    /**
     * 批量添加
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean saveBatch(List<UserAlarms> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 批量更新
     *
     * @param entityList
     * @return
     */
    @Transactional
    public boolean updateBatchByIdForDevice(List<UserAlarms> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        entityList.forEach(data -> {
            data.setUpdateTime(new Date());
            data.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            data.setVersion(data.getVersion() + 1);
        });
        return super.updateBatchById(entityList);
    }

    /**
     * 根据talId查询用户闹钟记录
     *
     * @param talId 用户ID
     * @return 用户闹钟记录列表
     */
    public List<UserAlarms> getByTalId(String talId) {
        LambdaQueryWrapper<UserAlarms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAlarms::getTalId, talId);
        return this.list(queryWrapper);
    }

    /**
     * 批量更新用户闹钟记录的talId和删除标记
     *
     * @param entityList 实体列表
     * @param encryptedTalId 加密后的talId
     * @return 更新是否成功
     */
    @Transactional
    public boolean updateBatchForUserLogout(List<UserAlarms> entityList, String encryptedTalId) {
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("UserAlarms entityList is empty, skip update");
            return true;
        }

        Date now = new Date();
        entityList.forEach(entity -> {
            // 设置加密后的talId
            entity.setTalId(encryptedTalId);
            // 设置删除标记为1
            entity.setDeleted(1);
            // 设置更新时间
            entity.setUpdateTime(now);
        });

        boolean result = super.updateBatchById(entityList);
        log.info("UserAlarms batch update completed, count: {}, result: {}", entityList.size(), result);
        return result;
    }

    /**
     * 处理用户登出时的用户闹钟数据
     *
     * @param talId 原始talId
     * @param encryptedTalId 加密后的talId
     */
    @Transactional
    public void processUserLogoutData(String talId,String encryptedTalId) {
        log.info("Processing UserAlarms data for user logout, talId: {}", talId);

        // 查询该用户的所有闹钟记录
        List<UserAlarms> alarmsList = getByTalId(talId);

        if (CollectionUtils.isEmpty(alarmsList)) {
            log.info("No UserAlarms found for talId: {}", talId);
            return;
        }
        // 批量更新
        updateBatchForUserLogout(alarmsList, encryptedTalId);

        log.info("UserAlarms data processing completed for talId: {}, processed count: {}", talId, alarmsList.size());
    }
}
