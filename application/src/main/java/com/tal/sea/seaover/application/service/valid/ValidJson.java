package com.tal.sea.seaover.application.service.valid;


import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Constraint(validatedBy = GsonJsonValidator.class)
public @interface ValidJson {
    String message() default "必须是有效的JSON结构";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}


