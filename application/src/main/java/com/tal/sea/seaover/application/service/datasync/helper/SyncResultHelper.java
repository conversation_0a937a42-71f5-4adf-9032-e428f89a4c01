package com.tal.sea.seaover.application.service.datasync.helper;

import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SyncResultHelper {

    /**
     * 通用的同步结果构建器
     *
     * @param syncStagingData 数据同步暂存对象
     * @param listByUnionIds  根据 unionId 批量查询的方法
     * @param converter       数据类型转换方法
     * @param <T>             请求类型（如 AlarmSyncRequest）
     * @param <D>             数据库实体类型（如 UserAlarms）
     * @param <R>             返回的响应类型（如 UserAlarmsSyncDataResponse）
     * @return SyncResult<R>
     */
    public static   <T, D, R> SyncResult<R> assemble(
            SyncStagingData<T> syncStagingData,
            Function<Set<String>, List<D>> listByUnionIds,
            Function<D, R> converter,
            Function<D, String> getUnionId) {

        SyncResult<R> result = new SyncResult<>();
        // 1. 提取所有 unionId
        Set<String> unionIds = Stream.of(
                        syncStagingData.getDeviceAdded(),
                        syncStagingData.getDeviceToAdd(),
                        syncStagingData.getDeviceToUpdate(),
                        syncStagingData.getDeviceToUpdateVersion(),
                        syncStagingData.getDeviceToDelete()
                )
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (unionIds.isEmpty()) {
            return result;
        }

        // 2. 批量查询
        List<D> entityList = listByUnionIds.apply(unionIds);
        if (CollectionUtils.isEmpty(entityList)) {
            return result;
        }
        //将entityList转换成Map key为unionId   value为entity
        Map<String, D> entityMap = entityList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(getUnionId, Function.identity(),(oldValue, newValue) -> newValue));

        // 3. 组装每个字段
        fill(result.getAdded(), syncStagingData.getDeviceAdded(), entityMap, converter);
        fill(result.getToAdd(), syncStagingData.getDeviceToAdd(), entityMap, converter);
        fill(result.getToUpdate(), syncStagingData.getDeviceToUpdate(), entityMap, converter);
        fill(result.getToUpdateVersion(), syncStagingData.getDeviceToUpdateVersion(), entityMap, converter);
        fill(result.getToDelete(), syncStagingData.getDeviceToDelete(), entityMap, converter);

        return result;
    }

    private static <D, R> void fill(List<R> targetList, List<String> unionIds, Map<String, D> entityMap, Function<D, R> converter) {
        if (!CollectionUtils.isEmpty(unionIds)) {
            for (String unionId : unionIds) {
                D entity = entityMap.get(unionId);
                if (entity != null) {
                    R response = converter.apply(entity);
                    if (response != null) {
                        targetList.add(response);
                    }
                }
            }
        }
    }

}
