package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.request.alarms.*;
import com.tal.sea.seaover.application.dto.response.alarms.UserAlarmsResponse;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.useralarms.UserAlarmsService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/alarm")
public class UserAlarmsController {
    @Autowired
    private UserAlarmsService userAlarmsService;
    @Resource
    private AlarmService alarmService;

    /**
     * 新增用户闹钟
     */
    @PostMapping("/add")
    public ResponseEntity addAlarm(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated AlarmsAddForDeviceRequest alarmsAddForDeviceRequest) {
        try {
            AlarmsAddRequest addRequest = new AlarmsAddRequest();
            BeanUtils.copyProperties(alarmsAddForDeviceRequest, addRequest);
            addRequest.setTalId(talId);
            addRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            addRequest.setEnabled(YesNoEnum.YES.getValue());
            UserAlarmsResponse alarmsResponse = userAlarmsService.addAlarm(addRequest);
            return ResponseUtil.successWithData(alarmsResponse);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("add addAlarm fail! exception:{}", e.getMessage());
            alarmService.alarm("add addAlarm fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.ADD_ALARM_FAIL.getMsg());
        }
    }


    /**
     * 获取用户闹钟
     */
    @PostMapping("/get")
    public ResponseEntity<UserAlarmsResponse> getAlarm(@RequestBody @Validated GetAlarmsRequest getAlarmsRequest) {
        UserAlarmsResponse alarmsResponse = userAlarmsService.getUserAlarmResponse(getAlarmsRequest.getAlarmId());
        return ResponseUtil.successWithData(alarmsResponse);
    }


    /**
     * 设置闹钟状态
     */
    @PostMapping("/setEnabled")
    public ResponseEntity setEnabled(@RequestBody @Validated SetAlarmsEnabledForDeviceRequest setAlarmsEnabledForDeviceRequest) {
        try {
            SetAlarmsEnabledRequest setAlarmsEnabledRequest = new SetAlarmsEnabledRequest();
            BeanUtils.copyProperties(setAlarmsEnabledForDeviceRequest, setAlarmsEnabledRequest);
            setAlarmsEnabledRequest.setAlarmId(setAlarmsEnabledForDeviceRequest.getId());
            setAlarmsEnabledRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            setAlarmsEnabledRequest.setUnionId(setAlarmsEnabledForDeviceRequest.getUnionId());

            UserAlarms result = userAlarmsService.setEnabled(setAlarmsEnabledRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            alarmService.alarm("set alarms enabled fail! reason:" + e.getMessage());
            log.error("set alarms enabled fail! exception:{}", e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.SET_ALARM_ENABLED_FAIL.getMsg());
        }
    }


    /**
     * 编辑闹钟
     */
    @PostMapping("/edit")
    public ResponseEntity edit(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated AlarmsEditForDeviceRequest alarmsEditForDeviceRequest) {
        try {
            AlarmsEditRequest alarmsEditRequest = new AlarmsEditRequest();
            BeanUtils.copyProperties(alarmsEditForDeviceRequest, alarmsEditRequest);
            alarmsEditRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            alarmsEditRequest.setTalId(talId);
            alarmsEditRequest.setUnionId(alarmsEditForDeviceRequest.getUnionId());
            UserAlarms result = userAlarmsService.edit(alarmsEditRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit alarms  fail! exception:{}", e.getMessage());
            alarmService.alarm("edit alarms  fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.EDIT_ALARM_FAIL.getMsg());
        }
    }


    /**
     * 删除用户闹钟
     */
    @PostMapping("/delete")
    public ResponseEntity deleteAlarm(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated DeleteAlarmsForDeviceRequest request) {
        try {
            DeleteAlarmsRequest deleteAlarmsRequest = new DeleteAlarmsRequest();
            deleteAlarmsRequest.setTalId(talId);
            deleteAlarmsRequest.setAlarmId(request.getId());
            deleteAlarmsRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            deleteAlarmsRequest.setUnionId(request.getUnionId());
            UserAlarms result = userAlarmsService.deleteAlarm(deleteAlarmsRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            alarmService.alarm("delete alarms  fail! reason:" + e.getMessage());
            log.error("delete alarms  fail! exception:{}", e.getMessage());
            return ResponseUtil.failWith500("delete alarms fail!");
        }
    }

}
