package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.service.pets.PetAlbumDataInitializer;
import com.tal.sea.seaover.application.dto.request.pets.*;
import com.tal.sea.seaover.application.dto.response.pets.PetAlbumResponse;
import com.tal.sea.seaover.application.dto.response.pets.PetHousePartsResponse;
import com.tal.sea.seaover.application.dto.response.pets.PetsResponse;
import com.tal.sea.seaover.application.constant.UserTypeConstant;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.pets.PetAlbumService;
import com.tal.sea.seaover.application.service.pets.PetHousePartsService;
import com.tal.sea.seaover.application.service.pets.PetsService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 宠物Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/pets")
public class PetsController {
    @Autowired
    private PetsService petsService;

    @Autowired
    private PetAlbumService petAlbumService;

    @Autowired
    private PetHousePartsService petHousePartsService;
    @Autowired
    private AlarmService alarmXtqService;

    @Autowired
    private PetAlbumDataInitializer petAlbumDataInitializer;

    @PostMapping("/list")
    public ResponseEntity<List<PetsResponse>> list(@Valid @RequestBody ListPetsRequest request) {
        List<PetsResponse> pets = petsService.list(request);
        return ResponseUtil.successWithData(pets);
    }


    @PostMapping("/verifyName")
    public ResponseEntity<Boolean> verifyName(@Valid @RequestBody VerifyNameRequest request) {
        boolean result = petsService.verifyName(request);
        return ResponseUtil.successWithData(result);
    }

    /**
     * 获取宠物相册数据
     *
     * @param request 请求参数
     * @return 宠物相册响应数据
     */
    @PostMapping("/album/list")
    public ResponseEntity<PetAlbumResponse> getAlbumData(@Valid @RequestBody PetAlbumRequest request) {
        log.info("获取宠物相册数据请求，petId: {}", request.getPetId());
        PetAlbumResponse response = petAlbumService.getAlbumData(request);
        return ResponseUtil.successWithData(response);
    }

    /**
     * 获取宠物小屋装扮数据
     *
     * @param request 请求参数
     * @return 宠物小屋装扮响应数据
     */
    @PostMapping("/house/parts")
    public ResponseEntity getHouseParts(@RequestHeader("X-Tal-Id") String talId, @Valid @RequestBody PetHousePartsRequest request) {
        try {
            log.info("获取宠物小屋装扮数据请求，petId: {}", request.getPetId());

            PetHousePartsResponse response = petHousePartsService.getHouseParts(talId, request.getPetId());
            return ResponseUtil.successWithData(response);
        } catch (Exception e) {
            log.error("获取宠物小屋装扮数据异常，talId: {}, 请求参数: {}", talId, request, e);
            alarmXtqService.alarm("getHouseParts 异常 错误信息: " + e.getMessage());
            return ResponseUtil.failWith500("Failed to get pet house decoration data");
        }
    }


    /**
     * 保存宠物小屋装扮数据
     *
     * @param talId   用户ID（请求头）
     *                userType 用户类型 1正式 2游客
     * @param request 保存请求参数（JSON对象）
     */
    @PostMapping("/house/parts/save")
    public ResponseEntity saveHouseParts(@RequestHeader("X-Tal-Id") String talId, @RequestHeader("X-User-Type") String userType, @Valid @RequestBody PetHousePartsSaveRequest request) {
        try {
            log.info("保存宠物小屋装扮数据请求，talId: {}, userType: {}, petId: {}, selectProp: {}",
                    talId, userType, request.getPetId(), request.getSelectProp());

            if (UserTypeConstant.GUEST.equals(userType)) {
                log.info("用户类型为游客，跳过保存宠物小屋装扮数据，talId: {}", talId);
                return ResponseUtil.successWithoutData();
            }

            petHousePartsService.saveHousePartsByLock(talId, request.getPetId(), request.getPbgId(), request.getSelectProp());
            return ResponseUtil.successWithoutData();
        } catch (Exception e) {
            log.error("保存宠物小屋装扮数据异常，talId: {}, 请求参数: {}", talId, request, e);
            alarmXtqService.alarm("saveHouseParts 异常 错误信息: " + e.getMessage());
            return ResponseUtil.failWith500("Failed to save pet house decoration data");
        }
    }

    /**
     * 刷新宠物相册数据缓存 - 内部接口
     */
    @PostMapping("/inner/album/refresh")
    public ResponseEntity refreshAlbumData() {
        try {
            log.info("开始刷新宠物相册数据缓存");
            petAlbumDataInitializer.refreshPetAlbumData();
            log.info("宠物相册数据缓存刷新成功");
            return ResponseUtil.successWithoutData();
        } catch (Exception e) {
            log.error("刷新宠物相册数据缓存失败", e);
            return ResponseUtil.failWith500("Failed to refresh pet album data cache");
        }
    }
}
