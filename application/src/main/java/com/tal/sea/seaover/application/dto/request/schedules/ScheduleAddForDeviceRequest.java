package com.tal.sea.seaover.application.dto.request.schedules;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class ScheduleAddForDeviceRequest implements Serializable {
    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId cannot be empty")
    private String unionId;
    /**
     * 设备端维护的此数据ID值
     */
    private Integer clientId;

    /**
     * 日程名称
     */
    @NotBlank(message = "name cannot be empty")
    private String name;

    /**
     * 日程时间 24小时制的秒数
     */
    @NotNull(message = "scheduleTime cannot be empty")
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    @NotNull(message = "notifyDuration cannot be empty")
    private Integer notifyDuration;

    /**
     * 重复日 1-7
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "repeating cannot be empty")
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * label颜色
     */
    @NotBlank(message = "colour cannot be empty")
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 是否预置日程 0-否，1-是
     */
    @NotNull(message = "isPreSet cannot be empty")
    private Integer isPreSet;

    /**
     * 预置日程类型
     */
    private Integer preSetType;
}