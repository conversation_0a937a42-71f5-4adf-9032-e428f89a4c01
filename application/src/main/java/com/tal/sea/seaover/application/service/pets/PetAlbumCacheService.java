package com.tal.sea.seaover.application.service.pets;

import com.tal.sea.seaover.application.entity.PetAlbumBackground;
import com.tal.sea.seaover.application.entity.PetAlbumProp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 宠物相册缓存服务
 * 管理背景和道具数据的内存缓存
 */
@Slf4j
@Service
public class PetAlbumCacheService {

    /**
     * 背景缓存 key: petId, value: List<PetAlbumBackground>
     */
    private Map<Integer, List<PetAlbumBackground>> backgroundCache = new ConcurrentHashMap<>();

    /**
     * 道具缓存 key: petId, value: List<PetAlbumProp>
     */
    private Map<Integer, List<PetAlbumProp>> propCache = new ConcurrentHashMap<>();

    /**
     * 背景ID索引 key: pbgId, value: PetAlbumBackground
     */
    private Map<String, PetAlbumBackground> backgroundIdIndex = new ConcurrentHashMap<>();

    /**
     * 道具ID索引 key: propId, value: PetAlbumProp
     */
    private Map<String, PetAlbumProp> propIdIndex = new ConcurrentHashMap<>();

    /**
     * 初始化背景缓存
     *
     * @param backgrounds 背景列表
     */
    public void initBackgroundCache(List<PetAlbumBackground> backgrounds) {
        if (backgrounds != null && !backgrounds.isEmpty()) {
            // 按petId分组
            Map<Integer, List<PetAlbumBackground>> groupedBackgrounds = backgrounds.stream()
                    .collect(Collectors.groupingBy(PetAlbumBackground::getPetId));

            // 引用替换
            Map<Integer, List<PetAlbumBackground>> newBackgroundCache = new ConcurrentHashMap<>(groupedBackgrounds);
            Map<String, PetAlbumBackground> newBackgroundIdIndex = new ConcurrentHashMap<>();
            
            // 建立ID索引
            backgrounds.forEach(bg -> newBackgroundIdIndex.put(bg.getPbgId(), bg));

            // 原子性替换引用
            this.backgroundCache = newBackgroundCache;
            this.backgroundIdIndex = newBackgroundIdIndex;

            log.info("背景缓存初始化完成，共{}个宠物的{}条背景记录", 
                    groupedBackgrounds.size(), backgrounds.size());
        }
    }

    /**
     * 初始化道具缓存
     *
     * @param props 道具列表
     */
    public void initPropCache(List<PetAlbumProp> props) {
        if (props != null && !props.isEmpty()) {
            // 按petId分组
            Map<Integer, List<PetAlbumProp>> groupedProps = props.stream()
                    .collect(Collectors.groupingBy(PetAlbumProp::getPetId));

            // 引用替换
            Map<Integer, List<PetAlbumProp>> newPropCache = new ConcurrentHashMap<>(groupedProps);
            Map<String, PetAlbumProp> newPropIdIndex = new ConcurrentHashMap<>();
            
            // 建立ID索引
            props.forEach(prop -> newPropIdIndex.put(prop.getPropId(), prop));

            // 原子性替换引用
            this.propCache = newPropCache;
            this.propIdIndex = newPropIdIndex;

            log.info("道具缓存初始化完成，共{}个宠物的{}条道具记录", 
                    groupedProps.size(), props.size());
        }
    }

    /**
     * 根据宠物ID获取背景列表
     *
     * @param petId 宠物ID
     * @return 背景列表
     */
    public List<PetAlbumBackground> getBackgroundsByPetId(Integer petId) {
        return backgroundCache.getOrDefault(petId, Collections.emptyList());
    }

    /**
     * 根据宠物ID获取道具列表
     *
     * @param petId 宠物ID
     * @return 道具列表
     */
    public List<PetAlbumProp> getPropsByPetId(Integer petId) {
        return propCache.getOrDefault(petId, Collections.emptyList());
    }

    /**
     * 根据背景ID获取背景信息
     *
     * @param pbgId 背景ID
     * @return 背景信息
     */
    public PetAlbumBackground getBackgroundByPbgId(String pbgId) {
        return backgroundIdIndex.get(pbgId);
    }

    /**
     * 根据道具ID获取道具信息
     *
     * @param propId 道具ID
     * @return 道具信息
     */
    public PetAlbumProp getPropByPropId(String propId) {
        return propIdIndex.get(propId);
    }


}