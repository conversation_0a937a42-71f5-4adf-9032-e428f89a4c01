package com.tal.sea.seaover.application.service.datasync.client.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.service.datasync.DataSyncProcessor;
import com.tal.sea.seaover.application.service.datasync.SyncDataRequestConverter;
import com.tal.sea.seaover.application.service.datasync.client.DataSyncClientStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PetsDataSyncClientStrategy implements DataSyncClientStrategy {

    @Autowired
    private SyncDataRequestConverter converter;

    @Autowired
    private DataSyncProcessor<SyncDataRequest.PetsSyncRequest, ?> processor;

    @Override
    public DataSyncProcessorEnum getType() {
        return DataSyncProcessorEnum.PETS;
    }

    @Override
    public SyncResult<?> sync(List<SyncDataParentRequest> requests, String talId) {
        List<SyncDataRequest.PetsSyncRequest> pets = requests.stream()
                .filter(data -> data instanceof SyncDataRequest.PetsSyncRequest)
                .map(data -> (SyncDataRequest.PetsSyncRequest) data)
                .toList();
        List<SyncDataRequest.PetsSyncRequest> deviceDatas = converter.convertPets(pets);
        //如果传入的requests数据中的Selected有多个选中的，默认取第一个，其他的设置为0
        if (!pets.isEmpty()) {
            boolean firstSelectedFound = false;
            for (SyncDataRequest.PetsSyncRequest pet : deviceDatas) {
                if (pet.getSelected() != null && pet.getSelected() == YesNoEnum.YES.getValue()) {
                    if (!firstSelectedFound) {
                        firstSelectedFound = true; // 保留第一个 selected = 1
                    } else {
                        pet.setSelected(YesNoEnum.NO.getValue()); // 将后续的 selected = 1 改为 0
                    }
                }
            }
        }
        return processor.process(talId, deviceDatas);
    }
}

