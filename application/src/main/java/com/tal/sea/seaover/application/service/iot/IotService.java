package com.tal.sea.seaover.application.service.iot;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.dto.request.iot.IotDirectMethodReq;
import com.tal.sea.seaover.application.dto.response.iot.IotResponse;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.util.OkHttpHelperUtil;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RefreshScope
public class IotService {
    /**
     * iot服务地址
     */
    @Value("${iot.service.url}")
    private String iotServiceUrl;
    @Autowired
    private AlarmService alarmService;
    @Autowired
    private ObjectMapper objectMapper;

    private final OkHttpClient client = OkHttpHelperUtil.createClient(6);


    /**
     * 闹钟iot同步数据
     *
     * @param sn         设备sn
     * @param userAlarms 闹钟
     */
    public void iotForAlarms(String sn, UserAlarms userAlarms) {
        // 将 UserAlarms 转换为 Map，添加 type 字段
        Map<String, Object> alarmMap = objectMapper.convertValue(userAlarms, new TypeReference<Map<String, Object>>() {
        });
        alarmMap.put("type", DataSyncProcessorEnum.ALARM.getValue());
        alarmMap.put("traceId", ThreadMdcUtil.getTraceId());
        //调用IOT服务
        IotDirectMethodReq iotDirectMethodReq = new IotDirectMethodReq(sn, "syncData",
                GsonUtil.toJson(alarmMap));
        iotDirectMethod(iotDirectMethodReq, false);
    }

    /**
     * 日程iot同步数据
     *
     * @param sn       设备sn
     * @param schedule 日程
     */
    public void iotForSchedule(String sn, UserSchedule schedule) {
        // 将 UserAlarms 转换为 Map，添加 type 字段
        Map<String, Object> scheduleMap = objectMapper.convertValue(schedule, new TypeReference<Map<String, Object>>() {
        });
        scheduleMap.put("type", DataSyncProcessorEnum.SCHEDULE.getValue());
        scheduleMap.put("traceId", ThreadMdcUtil.getTraceId());
        //调用IOT服务
        IotDirectMethodReq iotDirectMethodReq = new IotDirectMethodReq(sn, "syncData",
                GsonUtil.toJson(scheduleMap));
        iotDirectMethod(iotDirectMethodReq, false);
    }

    /**
     * 夜灯iot同步数据
     *
     * @param sn               设备sn
     * @param nightLightConfig 夜灯配置
     */
    public void iotForNightLightConfig(String sn, NightLightConfig nightLightConfig) {
        // 将 UserAlarms 转换为 Map，添加 type 字段
        Map<String, Object> nightLightConfigMap = objectMapper.convertValue(nightLightConfig, new TypeReference<Map<String, Object>>() {
        });
        nightLightConfigMap.put("type", DataSyncProcessorEnum.NIGHT_LIGHT.getValue());
        nightLightConfigMap.put("traceId", ThreadMdcUtil.getTraceId());
        //调用IOT服务
        IotDirectMethodReq iotDirectMethodReq = new IotDirectMethodReq(sn, "setNightLight",
                GsonUtil.toJson(nightLightConfigMap));
        iotDirectMethodReq.setWaitResult(true);
        iotDirectMethodReq.setConnectTimeoutInSeconds(5);
        iotDirectMethodReq.setResponseTimeoutInSeconds(5);
        iotDirectMethod(iotDirectMethodReq, true);
    }


    /**
     * 调用IOT服务
     */
    public void iotDirectMethod(IotDirectMethodReq iotDirectMethodReq, boolean isThrowException) {
        String traceId = ThreadMdcUtil.getTraceId();
        String requestParameter = toJson(iotDirectMethodReq);
        log.info("调用Iot服务 traceId:{} 参数:{}", traceId, requestParameter);
        if (StringUtils.isEmpty(requestParameter)) {
            log.error("调用Iot服务参数为空 traceId:{}", traceId);
            return;
        }
        try {
            Request request = OkHttpHelperUtil.buildJsonRequest(iotServiceUrl, requestParameter, createHeaders());
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                handlerException("调用Iot服务异常", isThrowException, true, requestParameter, response);
                return;
            }
            String body = response.body().string();
            if (StringUtils.isEmpty(body)) {
                handlerException("调用Iot服务responseBody为空", isThrowException, true, requestParameter, response);
                return;
            }
            log.info("调用Iot服务结果 traceId:{} responseBody: {}", traceId, body);
            IotResponse iotResponse = objectMapper.readValue(body, IotResponse.class);
            if (!iotResponse.getCode().equals("200")) {
                handlerException("调用Iot服务responseCode非200", isThrowException, false, requestParameter, response);
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用Iot服务发生异常 , request:{},  e:{}", requestParameter, e.getMessage(), e);
            alarmService.alarm("调用Iot服务发生异常, request:" + requestParameter + " || e.message: " + e.getMessage());
            if (isThrowException) {
                throw new BusinessException(ErrorEnum.IOT_SERVICE_CALL_FAIL);
            }
        }
    }

    private void handlerException(String errorMsg, boolean isThrowException, boolean isSendAlarm, String requestParameter, Response response) {
        String traceId = ThreadMdcUtil.getTraceId();
        log.error("{} traceId:{}, request:{}, response:{}", errorMsg, traceId, requestParameter, response);
        if (isSendAlarm) {
            alarmService.alarm(errorMsg + ", traceId: " + traceId + " || request: " + requestParameter +
                    " || response.code: " + response.code() + " || response.msg: " + response.message());
        }
        if (isThrowException) {
            throw new BusinessException(ErrorEnum.IOT_SERVICE_CALL_FAIL);
        }
    }


    private String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("转换json异常, e:{}", e.getMessage(), e);
            return "";
        }
    }


    private static Map<String, String> createHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put("x_trace_id", ThreadMdcUtil.getTraceId());
        return headers;
    }

}
