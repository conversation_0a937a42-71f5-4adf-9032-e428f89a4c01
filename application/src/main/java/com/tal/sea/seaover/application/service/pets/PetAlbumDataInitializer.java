package com.tal.sea.seaover.application.service.pets;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.entity.PetAlbumBackground;
import com.tal.sea.seaover.application.entity.PetAlbumProp;
import com.tal.sea.seaover.application.mapper.PetAlbumBackgroundMapper;
import com.tal.sea.seaover.application.mapper.PetAlbumPropMapper;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 宠物相册数据初始化器
 * 使用@PostConstruct在Bean初始化后加载数据到内存
 */
@Slf4j
@Component
@AllArgsConstructor
public class PetAlbumDataInitializer {
    private PetAlbumBackgroundMapper petAlbumBackgroundMapper;
    private PetAlbumPropMapper petAlbumPropMapper;
    private PetAlbumCacheService petAlbumCacheService;
    private CDNService cdnService;
    private AlarmService alarmService;

    /**
     * 初始化方法，在Bean创建并依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化宠物相册数据到内存...");
        
        try {
            // 加载背景数据
            loadBackgroundData();
            // 加载道具数据
            loadPropData();
            log.info("宠物相册数据初始化完成");
        } catch (Exception e) {
            log.error("宠物相册数据初始化失败", e);
            alarmService.alarm("宠物相册数据初始化失败" + e.getMessage());
        }
    }

    public void refreshPetAlbumData() {
        init();
    }

    /**
     * 加载背景数据到内存
     */
    private void loadBackgroundData() {
        log.info("加载宠物相册背景数据...");
        
        LambdaQueryWrapper<PetAlbumBackground> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(PetAlbumBackground::getPetId)
                   .orderByAsc(PetAlbumBackground::getSort);
        
        List<PetAlbumBackground> backgrounds = petAlbumBackgroundMapper.selectList(queryWrapper);
        
        // 为URL添加CDN前缀
        backgrounds.forEach(background -> {
            if (background.getThumbnailUrl() != null) {
                background.setThumbnailUrl(cdnService.getUrl() + background.getThumbnailUrl());
            }
            if (background.getFullImageUrl() != null) {
                background.setFullImageUrl(cdnService.getUrl() + background.getFullImageUrl());
            }
        });
        
        petAlbumCacheService.initBackgroundCache(backgrounds);
        
        log.info("加载宠物相册背景数据完成，共{}条记录", backgrounds.size());
    }

    /**
     * 加载道具数据到内存
     */
    private void loadPropData() {
        log.info("加载宠物相册道具数据...");
        
        LambdaQueryWrapper<PetAlbumProp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(PetAlbumProp::getPetId)
                   .orderByAsc(PetAlbumProp::getSort);
        
        List<PetAlbumProp> props = petAlbumPropMapper.selectList(queryWrapper);
        
        // 为URL添加CDN前缀
        props.forEach(prop -> {
            if (prop.getThumbnailUrl() != null) {
                prop.setThumbnailUrl(cdnService.getUrl() + prop.getThumbnailUrl());
            }
            if (prop.getFullImageUrl() != null) {
                prop.setFullImageUrl(cdnService.getUrl() + prop.getFullImageUrl());
            }
        });
        
        petAlbumCacheService.initPropCache(props);
        
        log.info("加载宠物相册道具数据完成，共{}条记录", props.size());
    }
}