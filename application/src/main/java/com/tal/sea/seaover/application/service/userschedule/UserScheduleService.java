package com.tal.sea.seaover.application.service.userschedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.constant.RedisConstant;
import com.tal.sea.seaover.application.dto.request.schedules.*;
import com.tal.sea.seaover.application.dto.response.schedule.UserScheduleResponse;
import com.tal.sea.seaover.application.dto.response.schedule.UserScheduleSyncDataResponse;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.DeleteStatusEnum;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.mapper.UserScheduleMapper;
import com.tal.sea.seaover.application.service.iot.IotService;
import com.tal.sea.seaover.application.service.resources.CubeResourcesService;
import com.tal.sea.seaover.application.util.DateUtils;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserScheduleService {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private UserScheduleMapper userScheduleMapper;

    @Autowired
    private IotService iotService;
    @Autowired
    private CubeResourcesService cubeResourcesService;

    /**
     * 新增日程
     *
     * @param requestParameter 请求参数
     * @return 日程
     */
    public UserScheduleResponse addSchedule(ScheduleAddRequest requestParameter) {
        log.info("addSchedule requestParameter:{}", GsonUtil.toJson(requestParameter));
        RLock lock = null;
        // 参数校验
        requestParameter.checkParameter();
        try {
            String talId = requestParameter.getTalId();
            // 获取日程用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_SCHEDULE_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            //判断unionId是否存在
            if (existsByUnionId(requestParameter.getUnionId(), talId)) {
                throw new BusinessException(ErrorEnum.UNION_ID_EXISTS);
            }
            //判断闹钟的数量是否超出100
            Integer notDeleteCount = getNotDeleteCount(talId);
            if (notDeleteCount > 100) {
                throw new BusinessException(ErrorEnum.SCHEDULE_LIMIT_REACHED);
            }
            // 重复日程
            if (requestParameter.getRepeating() == YesNoEnum.NO.getValue()) {
                String scheduleDay = requestParameter.getScheduleDay();
                if (StringUtils.isNotEmpty(scheduleDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(scheduleDay);
                    requestParameter.setRepeatDays(dayOfWeek);
                }
            }
            // 查询用户未删除的日程列表
            List<UserSchedule> userSchedules = queryNonDeletedSchedules(requestParameter.getTalId(), requestParameter.getScheduleTime());
            // 判断时间是否冲突
            boolean isTimeConflict = checkScheduleConflict(requestParameter.getRepeating(), requestParameter.getScheduleDay(), requestParameter.getRepeatDays(), userSchedules);
            if (isTimeConflict) {
                throw new BusinessException(ErrorEnum.TIME_CONFLICT);
            }
            // 新增日程
            UserSchedule newUserSchedule = insertUserSchedule(requestParameter);

            UserScheduleResponse userScheduleResponse = new UserScheduleResponse();
            BeanUtils.copyProperties(newUserSchedule, userScheduleResponse);

            //只有家长端修改了日程，才调用IOT服务
            if (Objects.equals(requestParameter.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForSchedule(requestParameter.getSn(), newUserSchedule);
            }
            log.info("addSchedule response: {}", GsonUtil.toJson(userScheduleResponse));
            return userScheduleResponse;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private UserSchedule insertUserSchedule(ScheduleAddRequest requestParameter) {
        UserSchedule userSchedule = new UserSchedule();
        BeanUtils.copyProperties(requestParameter, userSchedule);
        userSchedule.setCreateTime(new Date());
        userSchedule.setUpdateTime(new Date());
        userSchedule.setPreSetType(Objects.isNull(requestParameter.getPreSetType()) ? 0 : requestParameter.getPreSetType());
        Integer lastModifiedBy = requestParameter.getLastModifiedBy();
        userSchedule.setLastModifiedBy(lastModifiedBy);
        userSchedule.setVersion(1);
        userScheduleMapper.insert(userSchedule);
        return userSchedule;
    }

    /**
     * 比对输入的日程与查询的日程集合是否存在冲突
     *
     * @param repeating         是否循环日程
     * @param inputScheduleDay  输入的日程日期
     * @param inputRepeatDays   输入的重复日
     * @param existingSchedules 查询出的日程集合
     * @return true表示有冲突，false表示无冲突
     */
    public boolean checkScheduleConflict(Integer repeating, String inputScheduleDay, String inputRepeatDays, List<UserSchedule> existingSchedules) {
        if (existingSchedules == null || existingSchedules.isEmpty()) {
            return false;
        }

        //如果是循环日程
        if (Objects.equals(repeating, YesNoEnum.YES.getValue())) {
            Set<String> inputDaysSet = new HashSet<>(Arrays.asList(inputRepeatDays.split(",")));
            // 与日程比较：检查周几是否相同
            for (UserSchedule schedule : existingSchedules) {
                //排除掉一次性中未启用的日程
                if (schedule.getEnabled() == YesNoEnum.NO.getValue() &&
                        schedule.getRepeating() == YesNoEnum.NO.getValue()) {
                    continue;
                }
                String existingRepeatDays = schedule.getRepeatDays();
                if (StringUtils.isNotEmpty(existingRepeatDays)) {
                    Set<String> existingDaysSet = new HashSet<>(Arrays.asList(existingRepeatDays.split(",")));
                    if (inputDaysSet.stream().anyMatch(existingDaysSet::contains)) {
                        log.info("循环日程与库中的星期有冲突, time:{} userAlarmId:{} inputRepeatDay:{} serverRepeatDay:{}",
                                schedule.getScheduleTime(), schedule.getId(), inputDaysSet, existingRepeatDays);
                        return true;
                    }
                }
            }
        } else {
            //一次性日程
            List<UserSchedule> oneOffSchedules = existingSchedules.stream()
                    .filter(alarm -> alarm.getEnabled() == YesNoEnum.YES.getValue() &&
                            alarm.getRepeating() == YesNoEnum.NO.getValue()).toList();
            //重复性日程
            List<UserSchedule> repeatingSchedules = existingSchedules.stream()
                    .filter(schedule -> schedule.getRepeating() == YesNoEnum.YES.getValue()).toList();

            //判断是否和库里的一次性日程是否有相同日期的
            if (!oneOffSchedules.isEmpty() && StringUtils.isNotEmpty(inputScheduleDay)) {
                for (UserSchedule schedule : oneOffSchedules) {
                    if (inputScheduleDay.equals(schedule.getScheduleDay())) {
                        log.info("和库中的一次性日程比较日期冲突, time:{} userScheduleId:{} inputScheduleDay:{} serverScheduleDay:{}",
                                schedule.getScheduleTime(), schedule.getId(), inputScheduleDay, schedule.getScheduleDay());
                        return true;
                    }
                }

            }

            //判断是否和库里的重复性日程是否有相同星期的
            if (!repeatingSchedules.isEmpty() && StringUtils.isNotEmpty(inputRepeatDays)) {
                Set<String> inputDaysSet = new HashSet<>(Arrays.asList(inputRepeatDays.split(",")));
                for (UserSchedule schedule : repeatingSchedules) {
                    String existingRepeatDays = schedule.getRepeatDays();
                    if (StringUtils.isNotEmpty(existingRepeatDays)) {
                        Set<String> existingDaysSet = new HashSet<>(Arrays.asList(existingRepeatDays.split(",")));
                        if (inputDaysSet.stream().anyMatch(existingDaysSet::contains)) {
                            log.info("和库中的重复性日程比较星期冲突, time:{} userScheduleId:{} inputRepeatDay:{} serverRepeatDay:{}",
                                    schedule.getScheduleTime(), schedule.getId(), inputDaysSet, existingRepeatDays);
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 查询当前用户未删除且与输入scheduleTime匹配的日程集合
     *
     * @param talId        talId
     * @param scheduleTime 日程时间
     * @return 未删除且scheduleTime匹配的日程列表
     */
    public List<UserSchedule> queryNonDeletedSchedules(String talId, Integer scheduleTime) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getScheduleTime, scheduleTime)
                .eq(UserSchedule::getTalId, talId)
                .eq(UserSchedule::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return userScheduleMapper.selectList(queryWrapper);
    }

    /**
     * 查询当前用户未删除、与输入scheduleTime匹配且排除指定scheduleId的日程集合
     *
     * @param talId        用户talId
     * @param scheduleTime 日程时间
     * @param scheduleId   要排除的日程ID
     * @return 未删除、scheduleTime匹配且不包含指定scheduleId的日程列表
     */
    public List<UserSchedule> queryNonDeletedSchedulesExcludingId(String talId, Integer scheduleTime, Long scheduleId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getScheduleTime, scheduleTime)
                .eq(UserSchedule::getTalId, talId)
                .eq(UserSchedule::getDeleted, YesNoEnum.NO.getValue())
                .ne(UserSchedule::getId, scheduleId);
        return userScheduleMapper.selectList(queryWrapper);
    }

    /**
     * 根据scheduleId查询日程
     *
     * @param userScheduleId 参数
     * @return 日程实例
     */
    public UserSchedule getSchedule(Long userScheduleId) {
        return userScheduleMapper.selectById(userScheduleId);
    }

    /**
     * 根据scheduleId查询日程
     *
     * @param userScheduleId 参数
     * @return 日程实例
     */
    public UserScheduleResponse getScheduleResponse(Integer userScheduleId) {
        UserSchedule userSchedule = userScheduleMapper.selectById(userScheduleId);
        if (userSchedule == null) {
            return null;
        }
        UserScheduleResponse userScheduleResponse = new UserScheduleResponse();
        BeanUtils.copyProperties(userSchedule, userScheduleResponse);
        userScheduleResponse.setIconAndRingUrl(cubeResourcesService);
        return userScheduleResponse;
    }

    /**
     * 查询用户已开启未删除的日程数量
     *
     * @param requestParameter 请求参数
     * @return 已开启未删除的日程数量
     */
    public Integer getOpenCount(GetOpenCountRequest requestParameter) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, requestParameter.getTalId())
                .eq(UserSchedule::getEnabled, YesNoEnum.YES.getValue())
                .eq(UserSchedule::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return Math.toIntExact(userScheduleMapper.selectCount(queryWrapper));
    }


    /**
     * 查询用户未删除的日程数量
     *
     * @param talId 请求参数
     * @return 已开启未删除的日程数量
     */
    public Integer getNotDeleteCount(String talId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, talId)
                .eq(UserSchedule::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return Math.toIntExact(userScheduleMapper.selectCount(queryWrapper));
    }

    /**
     * 查询用户日程列表
     *
     * @param requestParameter 请求参数
     * @return 日程列表
     */
    public List<UserScheduleResponse> scheduleList(ScheduleListRequest requestParameter) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, requestParameter.getTalId())
                .eq(UserSchedule::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        queryWrapper.orderByAsc(UserSchedule::getScheduleTime);
        List<UserSchedule> userSchedules = userScheduleMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userSchedules)) {
            return null;
        }
        return userSchedules.stream().map(userSchedule -> {
            UserScheduleResponse userScheduleResponse = new UserScheduleResponse();
            BeanUtils.copyProperties(userSchedule, userScheduleResponse);
            userScheduleResponse.setIconAndRingUrl(cubeResourcesService);
            return userScheduleResponse;
        }).toList();
    }

    /**
     * 设置用户日程是否开启
     *
     * @param requestParameter 请求参数
     * @return 影响行数
     */
    public UserSchedule setEnabled(SetScheduleEnabledRequest requestParameter) {
        log.info("setEnabled(schedule) requestParameter:{}", GsonUtil.toJson(requestParameter));
        RLock lock = null;
        try {
            UserSchedule schedule = getSchedule(requestParameter.getScheduleId());

            // 如果按照scheduleId查询日程不存在，就按照unionId查询
            if (Objects.isNull(schedule) && StringUtils.isNotEmpty(requestParameter.getUnionId())) {
                log.info("scheduleId {} not found, try to find by unionId {}",
                        requestParameter.getScheduleId(), requestParameter.getUnionId());
                schedule = getScheduleByUnionId(requestParameter.getUnionId());
                if (Objects.isNull(schedule)) {
                    log.error("按照unionId {} 也查询不到日程", requestParameter.getUnionId());
                    throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
                }
                log.info("通过unionId {} 找到日程，scheduleId: {}", requestParameter.getUnionId(), schedule.getId());
            }

            if (Objects.isNull(schedule)) {
                throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
            }

            String talId = schedule.getTalId();
            // 获取日程用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_SCHEDULE_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);

            if (Objects.equals(schedule.getEnabled(), requestParameter.getEnabled())) {
                log.info("ignor request because repeat set UserSchedule enabled");
                return schedule;
            }

            //操作的是一次性日程，并且是要开启这个日程，需要进行日程冲突检测
            if (Objects.equals(requestParameter.getEnabled(), YesNoEnum.YES.getValue()) &&
                    Objects.equals(schedule.getRepeating(), YesNoEnum.NO.getValue())) {
                String scheduleDay = requestParameter.getScheduleDay();
                if (StringUtils.isEmpty(scheduleDay)) {
                    throw new BusinessException("开启日程scheduleDay不能为空");
                }
                List<UserSchedule> userSchedules = queryNonDeletedSchedulesExcludingId(schedule.getTalId(), schedule.getScheduleTime(), schedule.getId());
                boolean checkScheduleConflict = checkScheduleConflict(YesNoEnum.NO.getValue(), scheduleDay, DateUtils.calculateDayOfWeek(scheduleDay), userSchedules);
                if (checkScheduleConflict) {
                    throw new BusinessException(ErrorEnum.TIME_CONFLICT);
                }
                schedule.setScheduleDay(scheduleDay);
                schedule.setRepeatDays(DateUtils.calculateDayOfWeek(scheduleDay));
            }

            schedule.setEnabled(requestParameter.getEnabled());
            schedule.setLastModifiedBy(requestParameter.getLastModifiedBy());
            schedule.setUpdateTime(new Date());
            schedule.setVersion(schedule.getVersion() + 1);
            userScheduleMapper.updateById(schedule);
            //只有家长端修改了日程，才调用IOT服务
            if (Objects.equals(requestParameter.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                //调用IOT服务
                iotService.iotForSchedule(requestParameter.getSn(), schedule);
            }
            log.info("setEnabled(schedule) response: {}", GsonUtil.toJson(schedule));
            return schedule;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 根据unionId查询闹钟
     *
     * @param unionId unionId
     * @return 闹钟实例
     */
    public UserSchedule getScheduleByUnionId(String unionId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getUnionId, unionId);
        return userScheduleMapper.selectOne(queryWrapper);
    }

    /**
     * 编辑日程
     *
     * @param scheduleEditRequest 请求参数
     * @return 更新后的日程
     */
    public UserSchedule edit(ScheduleEditRequest scheduleEditRequest) {
        log.info("edit(schedule) requestParameter:{}", GsonUtil.toJson(scheduleEditRequest));
        RLock lock = null;
        // 参数校验
        scheduleEditRequest.checkParameter();
        try {
            String talId = scheduleEditRequest.getTalId();
            // 获取日程用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_SCHEDULE_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            UserSchedule schedule = getSchedule(scheduleEditRequest.getId());
            // 如果按照scheduleId查询日程不存在，就按照unionId查询
            if (Objects.isNull(schedule) && StringUtils.isNotEmpty(scheduleEditRequest.getUnionId())) {
                String unionId = scheduleEditRequest.getUnionId();
                log.info("edit schedule scheduleId {} not found, try to find by unionId {}",
                        scheduleEditRequest.getId(), unionId);
                schedule = getScheduleByUnionId(unionId);
                if (Objects.isNull(schedule)) {
                    log.error("编辑日程按照unionId {} 也查询不到日程", unionId);
                    throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
                }
                scheduleEditRequest.setId(schedule.getId());
                log.info("编辑日程通过unionId {} 找到日程，scheduleId: {}", unionId, schedule.getId());
            }
            if (Objects.isNull(schedule)) {
                throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
            }
            // 如果是一次性日程，将scheduleDay对应的周几放入repeatDays
            if (Objects.equals(scheduleEditRequest.getRepeating(), YesNoEnum.NO.getValue())) {
                String scheduleDay = scheduleEditRequest.getScheduleDay();
                if (StringUtils.isNotEmpty(scheduleDay)) {
                    String dayOfWeek = DateUtils.calculateDayOfWeek(scheduleDay);
                    scheduleEditRequest.setRepeatDays(dayOfWeek);
                }
            }

            // 查询用户未删除且不包含自身的日程列表
            List<UserSchedule> userSchedules = queryNonDeletedSchedulesExcludingId(
                    scheduleEditRequest.getTalId(), scheduleEditRequest.getScheduleTime(), schedule.getId());
            // 判断时间是否冲突
            boolean isTimeConflict = checkScheduleConflict(scheduleEditRequest.getRepeating(), scheduleEditRequest.getScheduleDay(), scheduleEditRequest.getRepeatDays(), userSchedules);
            if (isTimeConflict) {
                throw new BusinessException(ErrorEnum.TIME_CONFLICT);
            }
            updateUserSchedule(scheduleEditRequest, schedule);
            //只有家长端修改了日程，才调用IOT服务
            if (Objects.equals(scheduleEditRequest.getLastModifiedBy(), IdentityEnum.PARENT.getValue())) {
                // 调用IOT服务
                iotService.iotForSchedule(scheduleEditRequest.getSn(), schedule);
            }
            log.info("edit(schedule) response: {}", GsonUtil.toJson(schedule));
            return schedule;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 更新用户日程
     *
     * @param scheduleEditRequest 日程编辑请求参数
     * @param schedule            日程
     */
    private void updateUserSchedule(ScheduleEditRequest scheduleEditRequest, UserSchedule schedule) {
        BeanUtils.copyProperties(scheduleEditRequest, schedule);
        schedule.setUpdateTime(new Date());
        Integer lastModifiedBy = scheduleEditRequest.getLastModifiedBy();
        schedule.setLastModifiedBy(lastModifiedBy);
        schedule.setVersion(schedule.getVersion() + 1);
        userScheduleMapper.updateById(schedule);
    }



    /**
     * 删除日程
     *
     * @param deleteScheduleRequest 日程ID
     * @return 受影响行数
     */
    public UserSchedule deleteSchedule(DeleteScheduleRequest deleteScheduleRequest) {
        RLock lock = null;
        try {
            log.info("deleteSchedule requestParameter:{}", GsonUtil.toJson(deleteScheduleRequest));
            String talId = deleteScheduleRequest.getTalId();
            // 获取日程用户级锁
            lock = redissonClient.getLock(RedisConstant.USER_OPERATION_SCHEDULE_LOCK + talId);
            lock.lock(15, TimeUnit.SECONDS);
            UserSchedule schedule = getSchedule(deleteScheduleRequest.getScheduleId());
            // 如果按照scheduleId查询日程不存在，就按照unionId查询
            if (Objects.isNull(schedule) && StringUtils.isNotEmpty(deleteScheduleRequest.getUnionId())) {
                String unionId = deleteScheduleRequest.getUnionId();
                log.info("delete schedule scheduleId {} not found, try to find by unionId {}",
                        deleteScheduleRequest.getScheduleId(), unionId);
                schedule = getScheduleByUnionId(unionId);
                if (Objects.isNull(schedule)) {
                    log.error("删除日程按照unionId {} 也查询不到日程", unionId);
                    throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
                }
                log.info("删除日程通过unionId {} 找到日程，scheduleId: {}", unionId, schedule.getId());
            }
            if (Objects.isNull(schedule)) {
                throw new BusinessException(ErrorEnum.USER_SCHEDULE_NOT_FOUND);
            }
            schedule.setUpdateTime(new Date());
            Integer lastModifiedBy = deleteScheduleRequest.getLastModifiedBy();
            schedule.setLastModifiedBy(lastModifiedBy);
            schedule.setVersion(schedule.getVersion() + 1);
            Integer deleteStatus = getDeleteStatus(schedule, lastModifiedBy);
            schedule.setDeleted(deleteStatus);
            int result = userScheduleMapper.updateById(schedule);

            //只有家长端修改了日程，才调用IOT服务
            if (Objects.equals(lastModifiedBy, IdentityEnum.PARENT.getValue())) {
                // 调用IOT服务
                iotService.iotForSchedule(deleteScheduleRequest.getSn(), schedule);
            }
            log.info("deleteSchedule response: {}", result);
            return schedule;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取删除状态
     *
     * @param schedule       日程
     * @param lastModifiedBy 最后修改者
     * @return 删除状态
     */
    private Integer getDeleteStatus(UserSchedule schedule, Integer lastModifiedBy) {
        if (Objects.equals(lastModifiedBy, IdentityEnum.DEVICE.getValue())) {
            return DeleteStatusEnum.DELETE_SYNCED.getValue();
        } else if (Objects.equals(lastModifiedBy, IdentityEnum.PARENT.getValue())) {
            Integer clientId = schedule.getClientId();
            //家长端删除的时候如果clientId为空说明这个数据还未和设备端同步，直接变成3(不参与数据同步)
            if (clientId == null || clientId == 0) {
                return DeleteStatusEnum.DELETE_SYNCED.getValue();
            } else {
                return DeleteStatusEnum.DELETED.getValue();
            }
        }
        return DeleteStatusEnum.DELETED.getValue();
    }


    public List<UserScheduleSyncDataResponse> listByTalId(String talId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, talId);
        queryWrapper.ne(UserSchedule::getDeleted, DeleteStatusEnum.DELETE_SYNCED.getValue());
        List<UserSchedule> userAlarms = userScheduleMapper.selectList(queryWrapper);
        return userAlarms.stream().filter(Objects::nonNull).map(schedule -> {
            UserScheduleSyncDataResponse scheduleSyncData = new UserScheduleSyncDataResponse();
            BeanUtils.copyProperties(schedule, scheduleSyncData);
            return scheduleSyncData;
        }).collect(Collectors.toList());
    }

    public boolean existsByUnionId(String unionId, String talId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getUnionId, unionId);
        queryWrapper.eq(UserSchedule::getTalId, talId);
        return userScheduleMapper.selectCount(queryWrapper) > 0;
    }

    public UserSchedule add(UserSchedule userSchedule) {
        if (Objects.isNull(userSchedule)) {
            return null;
        }
        userSchedule.setVersion(1);
        userSchedule.setCreateTime(new Date());
        userSchedule.setUpdateTime(new Date());
        userScheduleMapper.insert(userSchedule);
        return userSchedule;
    }

    public UserSchedule update(UserSchedule userSchedule) {
        if (Objects.isNull(userSchedule)) {
            return null;
        }
        userSchedule.setUpdateTime(new Date());
        userSchedule.setVersion(userSchedule.getVersion() + 1);
        userScheduleMapper.updateById(userSchedule);
        return userSchedule;
    }

    public List<UserSchedule> listByUnionIds(String talId, Set<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return null;
        }
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserSchedule::getUnionId, unionIds);
        queryWrapper.eq(UserSchedule::getTalId, talId);
        return userScheduleMapper.selectList(queryWrapper);
    }

    /**
     * 判断用户是否存在指定类型的预设日程
     *
     * @param talId      用户id
     * @param preSetType 预设日程闹钟
     * @return true:存在
     */
    public boolean isExistPreSetAlarms(String talId, Integer preSetType) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, talId)
                .eq(UserSchedule::getIsPreSet, YesNoEnum.YES.getValue())
                .eq(UserSchedule::getPreSetType, preSetType)
                .eq(UserSchedule::getDeleted, DeleteStatusEnum.NO_DELETE.getValue());
        return userScheduleMapper.selectCount(queryWrapper) > 0;
    }


    public List<UserScheduleSyncDataResponse> listByAllTalId(String talId) {
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSchedule::getTalId, talId);
        List<UserSchedule> userAlarms = userScheduleMapper.selectList(queryWrapper);
        return userAlarms.stream().filter(Objects::nonNull).map(schedule -> {
            UserScheduleSyncDataResponse scheduleSyncData = new UserScheduleSyncDataResponse();
            BeanUtils.copyProperties(schedule, scheduleSyncData);
            return scheduleSyncData;
        }).collect(Collectors.toList());
    }

    /**
     * 根据UUID列表批量查询日程
     *
     * @param uuids UUID列表
     * @return 日程响应列表
     */
    public List<UserScheduleResponse> batchListByUuids(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return null;
        }
        LambdaQueryWrapper<UserSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserSchedule::getUnionId, uuids);
        List<UserSchedule> userSchedules = userScheduleMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userSchedules)) {
            return null;
        }
        return userSchedules.stream().map(userSchedule -> {
            UserScheduleResponse userScheduleResponse = new UserScheduleResponse();
            BeanUtils.copyProperties(userSchedule, userScheduleResponse);
            userScheduleResponse.setIconAndRingUrl(cubeResourcesService);
            return userScheduleResponse;
        }).toList();
    }
}