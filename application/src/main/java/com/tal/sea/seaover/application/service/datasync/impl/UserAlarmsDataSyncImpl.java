package com.tal.sea.seaover.application.service.datasync.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest.AlarmSyncRequest;
import com.tal.sea.seaover.application.dto.response.alarms.UserAlarmsSyncDataResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import com.tal.sea.seaover.application.entity.UserAlarms;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.enums.DeleteStatusEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.service.datasync.AbstractCubeDataSyncProcess;
import com.tal.sea.seaover.application.service.datasync.helper.SyncResultHelper;
import com.tal.sea.seaover.application.service.useralarms.UserAlarmsBatchService;
import com.tal.sea.seaover.application.service.useralarms.UserAlarmsService;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserAlarmsDataSyncImpl extends AbstractCubeDataSyncProcess<AlarmSyncRequest, UserAlarmsSyncDataResponse> {
    @Autowired
    private UserAlarmsService userAlarmsService;
    @Autowired
    private UserAlarmsBatchService userAlarmsBatchService;

    @Override
    public String getType() {
        return DataSyncProcessorEnum.ALARM.getType();
    }

    @Override
    protected List<UserAlarmsSyncDataResponse> getServerExistData(String talId) {
        return userAlarmsService.listByTalId(talId);
    }

    @Override
    protected List<UserAlarmsSyncDataResponse> getAllServerExistData(String talId) {
        return userAlarmsService.listAllByTalId(talId);
    }


    @Override
    protected boolean checkConflict(List<UserAlarmsSyncDataResponse> serverExistData, AlarmSyncRequest deviceData, UserAlarmsSyncDataResponse currentServerData) {
        //服务端数据为空，则无冲突
        if (CollectionUtils.isEmpty(serverExistData)) {
            return false;
        }
        if (Objects.isNull(deviceData)) {
            return false;
        }
        //排除掉当前数据currentServerData
        if (!Objects.isNull(currentServerData)) {
            serverExistData = serverExistData.stream()
                    .filter(data -> !Objects.equals(data.getId(), currentServerData.getId()))
                    .collect(Collectors.toList());
        }
        //如果设备端是一个关闭的一次性闹钟不进行冲突检测
        if (Objects.equals(deviceData.getRepeating(), YesNoEnum.NO.getValue()) &&
                Objects.equals(deviceData.getEnabled(), YesNoEnum.NO.getValue())) {
            return false;
        }
        //将serverExistData转换为Map key为：alarmTime value为响铃时间(HH:MM)相同的集合
        Map<Integer, List<UserAlarmsSyncDataResponse>> alarmTimeMap = serverExistData.stream()
                .collect(Collectors.groupingBy(
                        UserAlarmsSyncDataResponse::getAlarmTime // 直接按alarmTime分组
                ));
        List<UserAlarmsSyncDataResponse> sameTimeUserAlarms = alarmTimeMap.get(deviceData.getAlarmTime());
        //说明没有时间相同时间的数据就是无冲突
        if (CollectionUtils.isEmpty(sameTimeUserAlarms)) {
            return false;
        }
        //将sameTimeUserAlarms转换为List<UserAlarms> existingAlarms
        List<UserAlarms> existingAlarms = sameTimeUserAlarms.stream()
                .filter(alarm -> alarm.getDeleted() == YesNoEnum.NO.getValue())
                .map(alarm -> {
                    UserAlarms userAlarms = new UserAlarms();
                    BeanUtils.copyProperties(alarm, userAlarms);
                    return userAlarms;
                }).toList();
        return userAlarmsService.checkAlarmConflict(deviceData.getRepeating(), deviceData.getAlarmDay(),
                deviceData.getRepeatDays(), existingAlarms);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void executeOperationDb(SyncStagingData<AlarmSyncRequest> syncStagingData) {
        boolean existPreSetAlarms = userAlarmsService.isExistPreSetAlarms(syncStagingData.getTalId());
        //处理 serverToAdd（新增） 服务端新增数据
        List<UserAlarms> batchAdd = Optional.ofNullable(syncStagingData.getServerToAdd())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    if (Objects.equals(request.getIsPreSet(), YesNoEnum.YES.getValue()) && existPreSetAlarms) {
                        log.info("talId:{} 预置闹钟已存在，不做重复添加 request:{}", request.getTalId(), GsonUtil.toJson(request));
                        syncStagingData.getDeviceAdded().remove(request.getUnionId());
                        syncStagingData.getDeviceToDelete().add(request.getUnionId());
                        return null;
                    }
                    UserAlarms userAlarms = new UserAlarms();
                    userAlarms.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
                    BeanUtils.copyProperties(request, userAlarms);
                    return userAlarms;
                })
                .filter(Objects::nonNull)
                .toList();
        List<UserAlarms> batchUpdate = new ArrayList<>();
        // 处理 serverToUpdate
        Optional.ofNullable(syncStagingData.getServerToUpdate())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    UserAlarms userAlarms = userAlarmsService.getAlarm(request.getId());
                    if (Objects.nonNull(userAlarms)) {
                        BeanUtils.copyProperties(request, userAlarms);
                    }
                    return userAlarms;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);


        // 处理 serverToDelete
        Optional.ofNullable(syncStagingData.getServerToDelete())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    UserAlarms userAlarms = userAlarmsService.getAlarm(request.getId());
                    if (Objects.nonNull(userAlarms)) {
                        userAlarms.setDeleted(DeleteStatusEnum.DELETE_SYNCED.getValue());
                        userAlarmsService.update(userAlarms);
                    }
                    return userAlarms;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);

        if (!batchAdd.isEmpty()) {
            userAlarmsBatchService.saveBatch(batchAdd);
        }
        if (!batchUpdate.isEmpty()) {
            userAlarmsBatchService.updateBatchByIdForDevice(batchUpdate);
        }
    }

    @Override
    protected SyncResult<UserAlarmsSyncDataResponse> assemblyResultData(SyncStagingData<AlarmSyncRequest> syncStagingData) {
        return SyncResultHelper.assemble(
                syncStagingData,
                (unionIds) -> getUserAlarms(syncStagingData, unionIds),
                (this::convertToUserAlarmsResponse),
                UserAlarms::getUnionId);
    }


    private List<UserAlarms> getUserAlarms(SyncStagingData<AlarmSyncRequest> syncStagingData, Set<String> unionIds) {
        List<UserAlarms> userAlarms = userAlarmsService.listByUnionIds(syncStagingData.getTalId(), unionIds);
        if (userAlarms == null) {
            userAlarms = new ArrayList<>();
        }
        List<AlarmSyncRequest> deviceDatas = syncStagingData.getDeviceDatas();
        // 提取 userAlarms 中的 unionId 集合
        Set<String> existingUnionIds = userAlarms.stream()
                .map(UserAlarms::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        for (AlarmSyncRequest request : deviceDatas) {
            if (!existingUnionIds.contains(request.getUnionId())) {
                // 如果不存在，则添加到结果列表中
                UserAlarms userAlarm = new UserAlarms();
                BeanUtils.copyProperties(request, userAlarm);
                userAlarms.add(userAlarm);
            }
        }
        return userAlarms;
    }


    /**
     * 将 UserAlarms 转换为 UserAlarmsResponse
     */
    private UserAlarmsSyncDataResponse convertToUserAlarmsResponse(UserAlarms userAlarms) {
        if (userAlarms == null) {
            return null;
        }
        UserAlarmsSyncDataResponse response = new UserAlarmsSyncDataResponse();
        BeanUtils.copyProperties(userAlarms, response);
        response.setVersion(userAlarms.getVersion()); // 确保 version 字段正确映射
        return response;
    }
}