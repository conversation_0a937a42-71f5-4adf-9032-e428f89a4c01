package com.tal.sea.seaover.application.service.datasync.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest.ScheduleSyncRequest;
import com.tal.sea.seaover.application.dto.response.schedule.UserScheduleSyncDataResponse;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncStagingData;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.enums.DeleteStatusEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.service.datasync.AbstractCubeDataSyncProcess;
import com.tal.sea.seaover.application.service.datasync.helper.SyncResultHelper;
import com.tal.sea.seaover.application.service.userschedule.UserScheduleBatchService;
import com.tal.sea.seaover.application.service.userschedule.UserScheduleService;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserScheduleDataSyncImpl extends AbstractCubeDataSyncProcess<ScheduleSyncRequest, UserScheduleSyncDataResponse> {
    @Autowired
    private UserScheduleService userScheduleService;
    @Autowired
    private UserScheduleBatchService userScheduleBatchService;

    @Override
    public String getType() {
        return DataSyncProcessorEnum.SCHEDULE.getType();
    }

    @Override
    protected List<UserScheduleSyncDataResponse> getServerExistData(String talId) {
        return userScheduleService.listByTalId(talId);
    }

    @Override
    protected List<UserScheduleSyncDataResponse> getAllServerExistData(String talId) {
        return userScheduleService.listByAllTalId(talId);
    }


    @Override
    protected boolean checkConflict(List<UserScheduleSyncDataResponse> serverExistData, ScheduleSyncRequest deviceData, UserScheduleSyncDataResponse currentServerData) {
        //服务端数据为空，则无冲突
        if (CollectionUtils.isEmpty(serverExistData)) {
            return false;
        }
        if (Objects.isNull(deviceData)) {
            return false;
        }
        //排除掉当前数据currentServerData
        if (!Objects.isNull(currentServerData)) {
            serverExistData = serverExistData.stream()
                    .filter(data -> !Objects.equals(data.getId(), currentServerData.getId()))
                    .collect(Collectors.toList());
        }
        //如果设备端是一个关闭的一次性日程不进行冲突检测
        if (Objects.equals(deviceData.getRepeating(), YesNoEnum.NO.getValue()) &&
                Objects.equals(deviceData.getEnabled(), YesNoEnum.NO.getValue())) {
            return false;
        }
        //将serverExistData转换为Map key为：scheduleTime value为响铃时间(HH:MM)相同的集合
        Map<Integer, List<UserScheduleSyncDataResponse>> scheduleTimeMap = serverExistData.stream()
                .collect(Collectors.groupingBy(
                        UserScheduleSyncDataResponse::getScheduleTime // 直接按ScheduleTime分组
                ));
        List<UserScheduleSyncDataResponse> sameTimeUserSchedules = scheduleTimeMap.get(deviceData.getScheduleTime());
        //说明没有时间相同时间的数据
        if (CollectionUtils.isEmpty(sameTimeUserSchedules)) {
            return false;
        }
        //将sameTimeUserAlarms转换为List<UserAlarms> existingAlarms
        List<UserSchedule> existingSchedules = sameTimeUserSchedules.stream()
                .filter(alarm -> alarm.getDeleted() == YesNoEnum.NO.getValue())
                .map(alarm -> {
                    UserSchedule userSchedule = new UserSchedule();
                    BeanUtils.copyProperties(alarm, userSchedule);
                    return userSchedule;
                }).toList();
        return userScheduleService.checkScheduleConflict(deviceData.getRepeating(), deviceData.getScheduleDay(),
                deviceData.getRepeatDays(), existingSchedules);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void executeOperationDb(SyncStagingData<ScheduleSyncRequest> syncStagingData) {

        //处理 serverToAdd（新增） 服务端新增数据
        List<UserSchedule> batchAdd = Optional.ofNullable(syncStagingData.getServerToAdd())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    if (Objects.equals(request.getIsPreSet(), YesNoEnum.YES.getValue())) {
                        if (isExistDefaultSchedule(request.getTalId(), request.getPreSetType())) {
                            log.info("预置日程已存在，不做重复添加 request:{}", GsonUtil.toJson(request));
                            syncStagingData.getDeviceAdded().remove(request.getUnionId());
                            syncStagingData.getDeviceToDelete().add(request.getUnionId());
                            return null;
                        }
                    }
                    UserSchedule userSchedule = new UserSchedule();
                    BeanUtils.copyProperties(request, userSchedule);
                    return userSchedule;
                })
                .filter(Objects::nonNull)
                .toList();
        List<UserSchedule> batchUpdate = new ArrayList<>();
        // 处理 serverToUpdate
        Optional.ofNullable(syncStagingData.getServerToUpdate())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    UserSchedule userSchedule = userScheduleService.getSchedule(request.getId());
                    if (Objects.nonNull(userSchedule)) {
                        BeanUtils.copyProperties(request, userSchedule);
                    }
                    return userSchedule;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);


        // 处理 serverToDelete
        Optional.ofNullable(syncStagingData.getServerToDelete())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(request -> {
                    UserSchedule userSchedule = userScheduleService.getSchedule(request.getId());
                    if (Objects.nonNull(userSchedule)) {
                        userSchedule.setDeleted(DeleteStatusEnum.DELETE_SYNCED.getValue());
                        userScheduleService.update(userSchedule);
                    }
                    return userSchedule;
                })
                .filter(Objects::nonNull)
                .forEach(batchUpdate::add);

        if (!batchAdd.isEmpty()) {
            userScheduleBatchService.saveBatch(batchAdd);
        }
        if (!batchUpdate.isEmpty()) {
            userScheduleBatchService.updateBatchByIdForDevice(batchUpdate);
        }
    }

    @Override
    protected SyncResult<UserScheduleSyncDataResponse> assemblyResultData(SyncStagingData<ScheduleSyncRequest> syncStagingData) {
        return SyncResultHelper.assemble(
                syncStagingData,
                (unionIds) -> getUserSchedules(syncStagingData, unionIds),
                (this::convertToUserScheduleResponse),
                UserSchedule::getUnionId);
    }

    private List<UserSchedule> getUserSchedules(SyncStagingData<SyncDataRequest.ScheduleSyncRequest> syncStagingData, Set<String> unionIds) {
        List<UserSchedule> userSchedules = userScheduleService.listByUnionIds(syncStagingData.getTalId(), unionIds);
        if (CollectionUtils.isEmpty(userSchedules)) {
            userSchedules = new ArrayList<>();
        }
        List<SyncDataRequest.ScheduleSyncRequest> deviceDatas = syncStagingData.getDeviceDatas();
        // 提取 UserSchedules 中的 unionId 集合
        Set<String> existingUnionIds = userSchedules.stream()
                .map(UserSchedule::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        for (SyncDataRequest.ScheduleSyncRequest request : deviceDatas) {
            if (!existingUnionIds.contains(request.getUnionId())) {
                // 如果不存在，则添加到结果列表中
                UserSchedule userAlarm = new UserSchedule();
                BeanUtils.copyProperties(request, userAlarm);
                userSchedules.add(userAlarm);
            }
        }
        return userSchedules;
    }


    /**
     * 将 userSchedule 转换为 UserScheduleSyncDataResponse
     */
    private UserScheduleSyncDataResponse convertToUserScheduleResponse(UserSchedule userSchedule) {
        if (userSchedule == null) {
            return null;
        }
        UserScheduleSyncDataResponse response = new UserScheduleSyncDataResponse();
        BeanUtils.copyProperties(userSchedule, response);
        response.setVersion(userSchedule.getVersion()); // 确保 version 字段正确映射
        return response;
    }

    private boolean isExistDefaultSchedule(String talId, Integer preSetType) {
        return userScheduleService.isExistPreSetAlarms(talId, preSetType);
    }
}