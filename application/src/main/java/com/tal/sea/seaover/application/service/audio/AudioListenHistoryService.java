package com.tal.sea.seaover.application.service.audio;

import cn.hutool.json.JSONUtil;
import com.tal.sea.seaover.application.dto.request.audio.AddAudioListenHistoryRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioReportV2Request;
import com.tal.sea.seaover.application.dto.response.audio.AudioListResponse;
import com.tal.sea.seaover.application.entity.AudioListenHistory;
import com.tal.sea.seaover.application.enums.AudioListenEnum;
import com.tal.sea.seaover.application.mapper.AudioListenHistoryMapper;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class AudioListenHistoryService {

    @Autowired
    private AudioListenHistoryMapper audioListenHistoryMapper;

    public void add(@Valid AddAudioListenHistoryRequest request) {
        AudioListenHistory audioListenHistory = new AudioListenHistory();
        BeanUtils.copyProperties(request, audioListenHistory);
        audioListenHistoryMapper.insert(audioListenHistory);
    }

    public Boolean addV2(@Valid AudioReportV2Request request) {
        log.info("【熏听播放记录上报】AudioListenHistoryService.addV2 param:{}", JSONUtil.toJsonStr(request));
        AudioListenHistory audioListenHistory = new AudioListenHistory();
        BeanUtils.copyProperties(request, audioListenHistory);
        audioListenHistory.setAudioUnionId(request.getAudioId());
        audioListenHistory.setContentType(request.getGenresId());
        audioListenHistory.setType(obtainType(request));
        audioListenHistoryMapper.insert(audioListenHistory);
        return Boolean.TRUE;
    }

    private Integer obtainType(AudioReportV2Request request) {
        if (Objects.nonNull(request.getAlbumId()) && request.getAlbumId() > 0) {
            return AudioListenEnum.ListenHistoryType.ALBUM.getCode();
        }
        return AudioListenEnum.ListenHistoryType.LIST.getCode();
    }
}