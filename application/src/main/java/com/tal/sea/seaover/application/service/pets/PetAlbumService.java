package com.tal.sea.seaover.application.service.pets;

import com.tal.sea.seaover.application.dto.request.pets.PetAlbumRequest;
import com.tal.sea.seaover.application.dto.response.pets.PetAlbumBackgroundResponse;
import com.tal.sea.seaover.application.dto.response.pets.PetAlbumPropResponse;
import com.tal.sea.seaover.application.dto.response.pets.PetAlbumResponse;
import com.tal.sea.seaover.application.entity.PetAlbumBackground;
import com.tal.sea.seaover.application.entity.PetAlbumProp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 宠物相册服务
 */
@Slf4j
@Service
@AllArgsConstructor
public class PetAlbumService {
    
    private PetAlbumCacheService petAlbumCacheService;

    /**
     * 获取宠物相册数据
     *
     * @param request 请求参数
     * @return 宠物相册响应数据
     */
    public PetAlbumResponse getAlbumData(PetAlbumRequest request) {
        log.info("获取宠物相册数据，petId: {}", request.getPetId());
        
        PetAlbumResponse response = new PetAlbumResponse();
        
        // 获取背景数据
        List<PetAlbumBackgroundResponse> backgrounds = getBackgroundsByPetId(request.getPetId());
        response.setBackgrounds(backgrounds);
        
        // 获取道具数据
        List<PetAlbumPropResponse> props = getPropsByPetId(request.getPetId());
        response.setProps(props);
        
        log.info("获取宠物相册数据完成，backgrounds: {}, props: {}", 
                backgrounds != null ? backgrounds.size() : 0, 
                props != null ? props.size() : 0);
        
        return response;
    }

    /**
     * 根据宠物ID获取背景列表（从内存缓存中获取）
     *
     * @param petId 宠物ID
     * @return 背景列表
     */
    public List<PetAlbumBackgroundResponse> getBackgroundsByPetId(Integer petId) {
        List<PetAlbumBackground> backgrounds = petAlbumCacheService.getBackgroundsByPetId(petId);
        
        if (CollectionUtils.isEmpty(backgrounds)) {
            return new ArrayList<>();
        }
        
        return backgrounds.stream().map(background -> {
            PetAlbumBackgroundResponse response = new PetAlbumBackgroundResponse();
            BeanUtils.copyProperties(background, response);
            return response;
        }).toList();
    }

    /**
     * 根据宠物ID获取道具列表（从内存缓存中获取）
     *
     * @param petId 宠物ID
     * @return 道具列表
     */
    public List<PetAlbumPropResponse> getPropsByPetId(Integer petId) {
        List<PetAlbumProp> props = petAlbumCacheService.getPropsByPetId(petId);
        
        if (CollectionUtils.isEmpty(props)) {
            return new ArrayList<>();
        }
        
        return props.stream().map(prop -> {
            PetAlbumPropResponse response = new PetAlbumPropResponse();
            BeanUtils.copyProperties(prop, response);
            return response;
        }).toList();
    }

    /**
     * 根据背景ID获取背景信息（从内存缓存中获取）
     *
     * @param pbgId 背景ID
     * @return 背景信息
     */
    public PetAlbumBackground getBackgroundByPbgId(String pbgId) {
        return petAlbumCacheService.getBackgroundByPbgId(pbgId);
    }

    /**
     * 根据道具ID获取道具信息（从内存缓存中获取）
     *
     * @param propId 道具ID
     * @return 道具信息
     */
    public PetAlbumProp getPropByPropId(String propId) {
        return petAlbumCacheService.getPropByPropId(propId);
    }
}