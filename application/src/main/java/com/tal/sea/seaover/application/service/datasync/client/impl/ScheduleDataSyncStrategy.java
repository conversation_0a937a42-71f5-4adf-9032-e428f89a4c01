package com.tal.sea.seaover.application.service.datasync.client.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.service.datasync.DataSyncProcessor;
import com.tal.sea.seaover.application.service.datasync.SyncDataRequestConverter;
import com.tal.sea.seaover.application.service.datasync.client.DataSyncClientStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ScheduleDataSyncStrategy implements DataSyncClientStrategy {
    @Autowired
    private SyncDataRequestConverter converter;

    @Autowired
    private DataSyncProcessor<SyncDataRequest.ScheduleSyncRequest, ?> processor;

    @Override
    public DataSyncProcessorEnum getType() {
        return DataSyncProcessorEnum.SCHEDULE;
    }

    @Override
    public SyncResult<?> sync(List<SyncDataParentRequest> requests, String talId) {
        List<SyncDataRequest.ScheduleSyncRequest> schedules = requests.stream()
                .filter(data -> data instanceof SyncDataRequest.ScheduleSyncRequest)
                .map(data -> (SyncDataRequest.ScheduleSyncRequest) data)
                .toList();
        List<SyncDataRequest.ScheduleSyncRequest> deviceDatas = converter.convertSchedules(schedules);
        return processor.process(talId, deviceDatas);
    }
}
