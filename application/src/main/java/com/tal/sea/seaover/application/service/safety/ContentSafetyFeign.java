package com.tal.sea.seaover.application.service.safety;

import com.tal.sea.seaover.application.config.FeignClientConfig;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "so-601-public-application",contextId = "contentSafetyFeign",configuration = FeignClientConfig.class)
@Service
public interface ContentSafetyFeign {

    @PostMapping("/inner/public/content/safety/text")
    ResponseEntity<List<ContentSafetyResponse>> text(@RequestBody @Validated ContentSafetyTextRequest request);
}
