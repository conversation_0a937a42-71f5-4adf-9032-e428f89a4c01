package com.tal.sea.seaover.application.service.datasync.client.impl;

import com.tal.sea.seaover.application.dto.request.datasync.SyncDataParentRequest;
import com.tal.sea.seaover.application.dto.request.datasync.SyncDataRequest;
import com.tal.sea.seaover.application.dto.response.syncdata.SyncResult;
import com.tal.sea.seaover.application.enums.DataSyncProcessorEnum;
import com.tal.sea.seaover.application.service.datasync.DataSyncProcessor;
import com.tal.sea.seaover.application.service.datasync.SyncDataRequestConverter;
import com.tal.sea.seaover.application.service.datasync.client.DataSyncClientStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AlarmDataSyncClientStrategy implements DataSyncClientStrategy {
    @Autowired
    private SyncDataRequestConverter converter;

    @Autowired
    private DataSyncProcessor<SyncDataRequest.AlarmSyncRequest, ?> processor;

    @Override
    public DataSyncProcessorEnum getType() {
        return DataSyncProcessorEnum.ALARM;
    }

    @Override
    public SyncResult<?> sync(List<SyncDataParentRequest> requests, String talId) {
        List<SyncDataRequest.AlarmSyncRequest> alarms = requests.stream()
                .filter(data -> data instanceof SyncDataRequest.AlarmSyncRequest)
                .map(data -> (SyncDataRequest.AlarmSyncRequest) data)
                .toList();
        List<SyncDataRequest.AlarmSyncRequest> deviceDatas = converter.convertAlarms(alarms);
        return processor.process(talId, deviceDatas);
    }
}
