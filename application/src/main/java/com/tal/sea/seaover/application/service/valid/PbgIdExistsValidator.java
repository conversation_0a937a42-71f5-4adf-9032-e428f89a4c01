package com.tal.sea.seaover.application.service.valid;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.tal.sea.seaover.application.service.pets.PetAlbumCacheService;

public class PbgIdExistsValidator implements ConstraintValidator<PbgIdExists, String> {

    @Autowired
    private PetAlbumCacheService petAlbumCacheService;

    @Override
    public boolean isValid(String pbgId, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(pbgId)) {
            return false;
        }
        return petAlbumCacheService.getBackgroundByPbgId(pbgId) != null;
    }
}
