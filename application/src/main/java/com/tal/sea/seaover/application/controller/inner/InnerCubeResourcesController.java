package com.tal.sea.seaover.application.controller.inner;


import com.tal.sea.seaover.application.dto.request.resources.CubeResourcesAddReq;
import com.tal.sea.seaover.application.entity.CubeResources;
import com.tal.sea.seaover.application.service.resources.CubeResourcesService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/inner/datahub/cube/resources")
public class InnerCubeResourcesController {
    @Autowired
    private CubeResourcesService cubeResourcesService;

    /**
     * 查询闹钟图标和铃声
     */
    @GetMapping("/listAlarmsIconAndSound")
    public ResponseEntity listAlarmsIconAndSound() {
        return ResponseUtil.successWithData(cubeResourcesService.listAlarmsIconAndSound());
    }

    /**
     * 查询日程图标
     */
    @GetMapping("/listScheduleIcon")
    public ResponseEntity listScheduleIcon() {
        return ResponseUtil.successWithData(cubeResourcesService.listScheduleIcon());
    }

    /**
     * 刷新资源缓存
     */
    @GetMapping("/refreshCache")
    public ResponseEntity refreshCache() {
        cubeResourcesService.refreshCache();
        return ResponseUtil.successWithoutData();
    }

    /**
     * 新增资源
     */
    @PostMapping("/add")
    public ResponseEntity addResource(@RequestBody @Valid CubeResourcesAddReq cubeResourcesAddReq) {
        CubeResources cubeResources = cubeResourcesService.addResource(cubeResourcesAddReq);
        return ResponseUtil.successWithData(cubeResources);
    }
}
