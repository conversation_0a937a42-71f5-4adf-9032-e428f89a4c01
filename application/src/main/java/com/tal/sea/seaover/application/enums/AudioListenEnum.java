package com.tal.sea.seaover.application.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/15
 */
public class AudioListenEnum {

    /**
     * 年龄段
     */
    @AllArgsConstructor
    @Getter
    public enum AgeRange {

        ALL(0, "All"),
        ZERO_TO_TWO(1, "0-2"),
        THREE_TO_FIVE(2, "3-5"),
        SIX_TO_EIGHT(3, "6-8"),
        NINE(4, "9+"),
        ;

        private final Integer code;
        private final String desc;
    }

    /**
     * 风格类型
     */
    @AllArgsConstructor
    @Getter
    public enum Genres {

        ALL(0, "All"),
        MUSIC(2, "Music"),
        STORY(1, "Story"),
        PODCAST(3, "PodCast"),
        ;

        private final Integer code;
        private final String desc;
    }

    /**
     * 播放记录类型
     */
    @AllArgsConstructor
    @Getter
    public enum ListenHistoryType {

        LIST(1, "播放列表"),
        ALBUM(2, "专辑列表"),
        ;

        private final Integer code;
        private final String desc;
    }
    /**
     * 动作
     */
    @AllArgsConstructor
    @Getter
    public enum Action {

        NEXT(1, "下一首"),
        PRE(2, "上一首"),
        ;

        private final Integer code;
        private final String desc;
    }


} 