package com.tal.sea.seaover.application.dto.request.alarms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetAlarmsEnabledRequest implements Serializable {
    /**
     * alarmId
     */
    @NotNull(message = "alarmId cannot be empty")
    private Long alarmId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 是否启用
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * 最后修改人
     */
    @NotNull(message = "lastModifiedBy cannot be empty")
    private Integer lastModifiedBy;

    /**
     * unionId，用于备用查询
     */
    private String unionId;
}
