package com.tal.sea.seaover.application.enums;


/**
 * <AUTHOR> 2025-05-08
 */
public enum DeleteStatusEnum {

    NO_DELETE(0, "未删除"),
    DELETED(1, "已删除"),
    /**
     * 此值使用的场景是
     * 1.设备端发起的删除，状态直接赋值此值
     * 2.家长端发起的删除，状态赋值为1，待设备端再次同步时未提交，状态再赋值为2
     */
    DELETE_SYNCED(2, "删除状态已被同步");

    private int value;

    private String description;

    DeleteStatusEnum(int _value, String _description) {
        this.value = _value;
        this.description = _description;
    }


    public int getValue() {
        return value;
    }


    public void setValue(int value) {
        this.value = value;
    }


    public String getDescription() {
        return description;
    }


    public void setDescription(String description) {
        this.description = description;
    }

    public static DeleteStatusEnum valueOfType(int value) {
        for (DeleteStatusEnum type : DeleteStatusEnum.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }

    public static boolean contains(Integer value) {
        for (DeleteStatusEnum c : DeleteStatusEnum.values()) {
            if (c.value == value) {
                return true;
            }
        }
        return false;
    }
}
