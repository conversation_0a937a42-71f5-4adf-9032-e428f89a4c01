package com.tal.sea.seaover.application.service.audio;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tal.sea.seaover.application.dto.request.audio.AddAudioRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioListRequest;
import com.tal.sea.seaover.application.dto.request.audio.AudioListV2Request;
import com.tal.sea.seaover.application.dto.request.audio.DeleteAudioRequest;
import com.tal.sea.seaover.application.dto.response.audio.*;
import com.tal.sea.seaover.application.entity.AlbumDetail;
import com.tal.sea.seaover.application.entity.Audio;
import com.tal.sea.seaover.application.entity.AudioAlbum;
import com.tal.sea.seaover.application.entity.AudioListenHistory;
import com.tal.sea.seaover.application.enums.AudioListenEnum;
import com.tal.sea.seaover.application.enums.SceneModeEnum;
import com.tal.sea.seaover.application.enums.SkipEnum;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.mapper.AlbumDetailMapper;
import com.tal.sea.seaover.application.mapper.AudioAlbumMapper;
import com.tal.sea.seaover.application.mapper.AudioListenHistoryMapper;
import com.tal.sea.seaover.application.mapper.AudioMapper;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.xpod.tools.util.GsonUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AudioService {

    @Autowired
    private AudioMapper audioMapper;

    @Autowired
    private AudioListenHistoryMapper audioListenHistoryMapper;

    @Autowired
    private AudioCacheService audioCacheService;
    @Autowired
    private AudioAlbumMapper audioAlbumMapper;
    @Autowired
    private AlbumDetailMapper albumDetailMapper;
    @Autowired
    private AlarmService alarmService;
    @Value("${cdn.domain:}")
    private String cdnDomain;

    public List<Audio> audioList = Lists.newArrayList();
    public List<AudioAlbum> audioAlbumList = Lists.newArrayList();
    public Map<Long,List<String>> albumDetailMap = new HashMap<>();


    @PostConstruct
    public void init() {
        log.info("初始化音频数据开始");
        this.audioList = audioMapper.selectList(Wrappers.<Audio>lambdaQuery().orderByAsc(Audio::getSequence));
        this.audioAlbumList =  audioAlbumMapper.selectList(Wrappers.<AudioAlbum>lambdaQuery().orderByAsc(AudioAlbum::getSort));
        List<AlbumDetail> albumDetails = albumDetailMapper.selectList(Wrappers.lambdaQuery());
        this.albumDetailMap = albumDetails.stream().collect(Collectors.groupingBy(AlbumDetail::getAlbumId,Collectors.mapping(AlbumDetail::getAudioId, Collectors.toList())));
        log.info("初始化音频数据完成");
    }


    /**
     * 根据请求参数查询音频列表
     */
    public List<AudioListResponse> list(AudioListRequest request) {
        log.info("list audio query parameter:{}", GsonUtil.toJson(request));
        List<Audio> audioList;
        // 确认起始序号
        int startSequence = getStartSequence(request);
        log.info("query audio list startSequence:{}", startSequence);
        boolean isExistInCache = audioCacheService.isAudioCacheEmpty(request.getSceneMode());
        try {
            if (isExistInCache) {
                log.info("audio in the cache.");
                boolean forward = Objects.equals(SkipEnum.NEXT.getValue(), request.getSkip());
                audioList = audioCacheService.getAudioRange(request.getSceneMode(), startSequence, request.getPageNumber(), forward);
                return convertResponse(audioList);
            }
        } catch (Exception e) {
            log.error("query audio list from redis error.", e);
            alarmService.alarm("从缓存中查询闹钟熏听音频列表失败 reason:" + e.getMessage());
        }
        log.info("audio not in the cache. query db");
        audioList = loadAudioFromDb(request, startSequence);
        // 转换返回结果
        return convertResponse(audioList);
    }

    private List<AudioListResponse> convertResponse(List<Audio> audioList) {
        return audioList.stream().map(audio -> {
            AudioListResponse response = new AudioListResponse();
            BeanUtils.copyProperties(audio, response);
            return response;
        }).collect(Collectors.toList());
    }

    private List<Audio> loadAudioFromDb(AudioListRequest request, int startSequence) {
        List<Audio> audioList;
        log.info("audio not in the cache query db");
        //计算查询范围
        List<Integer> sequenceRange = determineSequenceRange(request, startSequence);
        //从数据库中查询音频列表
        audioList = queryAudioFromDb(request.getSceneMode(), sequenceRange);
        return audioList;
    }

    /**
     * 确认查询音频的起始序号
     *
     * @param request 请求对象
     * @return 起始序号
     */
    private int getStartSequence(AudioListRequest request) {
        String latestAudioUnionId = null;
        Long latestListenTime = null;

        //从请求参数 AudioListRequest 中找出最近的音频收听记录（按时间最新)
        if (!CollectionUtils.isEmpty(request.getListenRecord())) {
            for (AudioListRequest.ListenRecord record : request.getListenRecord()) {
                if (latestListenTime == null || record.getListenTime() > latestListenTime) {
                    latestAudioUnionId = record.getUnionId();
                    latestListenTime = record.getListenTime();
                }
            }
        }
        // 查询数据库中该用户 (talId) 最近一次收听的音频历史记录
        AudioListenHistory latestHistory = getLatestHistory(request);
        //比较请求中的记录与数据库记录，取时间最新的那条
        if (latestHistory != null && (latestListenTime == null || latestHistory.getListenTime() > latestListenTime)) {
            latestAudioUnionId = latestHistory.getAudioUnionId();
        }

        // 如果没有收听记录，默认起始序号为1
        if (latestAudioUnionId == null) {
            log.info("no listen history. startSequence is 1");
            return 1;
        }
        // 根据音频ID查询对应的sequence
        Audio audio = getAudioByUnionId(latestAudioUnionId);
        if (Objects.isNull(audio)) {
            return 1;
        }
        return audio.getSequence();
    }

    /**
     * 根据音频标识查询音频（不包含已删除的音频）
     *
     * @param latestAudioUnionId 最近一次音频标识
     * @return 音频
     */
    private Audio getAudioByUnionId(String latestAudioUnionId) {
        return audioMapper.selectOne(new LambdaQueryWrapper<Audio>().eq(Audio::getUnionId, latestAudioUnionId));
    }

    /**
     * 根据音频标识查询音频（包含所有状态的音频，用于删除操作）
     *
     * @param unionId 音频标识
     * @return 音频
     */
    private Audio getAudioByUnionIdForDelete(String unionId) {
        return audioMapper.selectOne(new LambdaQueryWrapper<Audio>().eq(Audio::getUnionId, unionId));
    }

    /**
     * 查询最近一次历史记录
     *
     * @param request 请求对象
     * @return
     */
    private AudioListenHistory getLatestHistory(AudioListRequest request) {
        LambdaQueryWrapper<AudioListenHistory> historyQuery = new LambdaQueryWrapper<>();
        historyQuery.eq(AudioListenHistory::getTalId, request.getTalId()).eq(AudioListenHistory::getSceneMode, request.getSceneMode())
                .orderByDesc(AudioListenHistory::getListenTime).last("LIMIT 1");
        return audioListenHistoryMapper.selectOne(historyQuery);
    }

    /**
     * 确定查询范围
     *
     * @param request       请求对象
     * @param startSequence 起始序号
     * @return 查询范围
     */
    private List<Integer> determineSequenceRange(AudioListRequest request, int startSequence) {
        // 获取总记录数
        int totalSize = getTotalSizeFromDb(request);
        if (totalSize == 0) {
            log.info("There is no audio in the database.");
            return Collections.emptyList();
        }
        List<Integer> sequenceRange = new ArrayList<>();
        int pageNumber = request.getPageNumber();
        int skip = request.getSkip(); // 1: prev, 2: next

        if (skip == SkipEnum.NEXT.getValue()) { // next
            for (int i = 0; i < pageNumber && sequenceRange.size() < totalSize; i++) {
                int seq = (startSequence + i) % totalSize;
                if (seq == 0) {
                    seq = totalSize;
                }
                sequenceRange.add(seq);
            }
        } else { // prev
            for (int i = 0; i < pageNumber && sequenceRange.size() < totalSize; i++) {
                int seq = (startSequence - i) % totalSize;
                if (seq <= 0) {
                    seq += totalSize;
                }
                sequenceRange.add(seq);
            }
        }

        return sequenceRange;
    }

    private int getTotalSizeFromDb(AudioListRequest request) {
        LambdaQueryWrapper<Audio> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Audio::getSceneMode, request.getSceneMode());
        return audioMapper.selectCount(queryWrapper).intValue();
    }

    private List<Audio> queryAudioFromDb(int sceneMode, List<Integer> sequenceRange) {
        if (CollectionUtils.isEmpty(sequenceRange)) {
            return Collections.emptyList();
        }
        // 如果Redis中为空，从数据库查询（排除已删除的音频）
        LambdaQueryWrapper<Audio> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Audio::getSceneMode, sceneMode).in(Audio::getSequence, sequenceRange);
        List<Audio> audioList = audioMapper.selectList(queryWrapper);

        // 按照传入的sequenceRange顺序排序结果
        if (CollectionUtils.isEmpty(audioList)) {
            return audioList;
        }

        // 创建sequence到Audio的映射
        Map<Integer, Audio> sequenceToAudioMap = audioList.stream().collect(Collectors.toMap(Audio::getSequence, audio -> audio));

        // 按照sequenceRange的顺序重新组织结果
        List<Audio> orderedResult = new ArrayList<>();
        for (Integer seq : sequenceRange) {
            Audio audio = sequenceToAudioMap.get(seq);
            if (audio != null) {
                audio.setAudioUrl(cdnDomain + audio.getAudioUrl());
                audio.setCoverUrl(cdnDomain + audio.getCoverUrl());
                orderedResult.add(audio);
            }
        }
        return orderedResult;
    }

    /**
     * 新增音频
     *
     * @param request
     */
    public void add(AddAudioRequest request) {
        Audio audio = new Audio();
        BeanUtils.copyProperties(request, audio);
        audio.setUnionId(UUID.randomUUID().toString());
        audio.setDeleted(YesNoEnum.NO.getValue());
        audioMapper.insert(audio);
    }

    /**
     * 删除音频（逻辑删除）
     *
     * @param request 删除请求
     */
    public void delete(DeleteAudioRequest request) {
        // 根据unionId查询音频是否存在（包含所有状态）
        Audio audio = getAudioByUnionIdForDelete(request.getUnionId());
        if (Objects.isNull(audio)) {
            throw new BusinessException("音频不存在，无法删除");
        }

        // 检查音频是否已经被删除
        Integer deletedStatus = audio.getDeleted();
        if (deletedStatus != null && Objects.equals(deletedStatus, YesNoEnum.YES.getValue())) {
            throw new BusinessException("音频已被删除，无法重复删除");
        }

        // 执行逻辑删除操作
        Audio updateAudio = new Audio();
        updateAudio.setId(audio.getId());
        updateAudio.setDeleted(YesNoEnum.YES.getValue());
        audioMapper.updateById(updateAudio);
    }

    public AudioAlbumResponse getAlbumList(String talId) {
        log.info("【获取专辑列表】AudioService.getAlbumList param:{}", talId);
        AudioAlbumResponse response = new AudioAlbumResponse();
        //读取内存
        if(CollUtil.isEmpty(audioAlbumList)){
            return response;
        }
        //查询tb_audio_listen_history 表，获取最近一次听的歌曲，当时是否选择专辑
        AudioListenHistory audioListenHistory = audioListenHistoryMapper.selectOne(Wrappers.<AudioListenHistory>lambdaQuery().eq(AudioListenHistory::getTalId, talId).orderByDesc(AudioListenHistory::getListenTime).last("LIMIT 1"));
        Long lastAlbumId=0L;
        if(Objects.nonNull(audioListenHistory) && audioListenHistory.getAlbumId()>0){
            lastAlbumId = audioListenHistory.getAlbumId();
        }
        List<AlbumDTO> albumList =convertAlbumDTOs(audioAlbumList,lastAlbumId);
        response.setAlbumList(albumList);
        log.info("【获取专辑列表】AudioService.getAlbumList param:{},result:{}", talId,JSONUtil.toJsonStr(response));
        return response;
    }

    /**
     * 转换返回对象
     * @param audioAlbums
     * @param lastAlbumId
     * @return
     */
    private List<AlbumDTO> convertAlbumDTOs(List<AudioAlbum> audioAlbums, Long lastAlbumId) {
        return audioAlbums.stream().map(audioAlbum -> {
            AlbumDTO albumDTO = new AlbumDTO();
            albumDTO.setAlbumId(audioAlbum.getId());
            albumDTO.setAlbumName(audioAlbum.getName());
            albumDTO.setCoverUrl(cdnDomain+audioAlbum.getCoverUrl());
            albumDTO.setSort(audioAlbum.getSort());
            albumDTO.setIsChecked(audioAlbum.getId().equals(lastAlbumId));
            return albumDTO;
        }).toList();
    }

    public AudioOptionsResponse getAudioOptions(String talId, Integer sceneMode) {
        log.info("【获取playlist筛选项】AudioService.getAudioOptions param:{},{}", talId,sceneMode);
        AudioOptionsResponse response = new AudioOptionsResponse();
        //查询tb_audio_listen_history 表，获取最近一次听的歌曲，当时勾选age、Genres
        AudioListenHistory audioListenHistory = audioListenHistoryMapper.selectOne(Wrappers.<AudioListenHistory>lambdaQuery()
                .eq(Objects.nonNull(sceneMode), AudioListenHistory::getSceneMode, sceneMode)
                .eq(AudioListenHistory::getTalId, talId)
                .orderByDesc(AudioListenHistory::getListenTime).last("LIMIT 1"));
        //最近勾选的年龄段id
        Long lastAgeRangeId=0L;
        if(Objects.nonNull(audioListenHistory) && audioListenHistory.getAgeRangeId()>0){
            lastAgeRangeId = audioListenHistory.getAgeRangeId();
        }
        //最近勾选的风格类型id
        Long lastGenresId=0L;
        //如果最近一次选择的PodCast 并且请求的是夜间模式，由于夜间不存在PodCast，所以置为0
        if (Objects.nonNull(audioListenHistory) && AudioListenEnum.Genres.PODCAST.getCode().equals(audioListenHistory.getContentType()) && Objects.equals(SceneModeEnum.SLEEP_MODE.getValue(), sceneMode)) {
            lastGenresId=0L;
        }else if(Objects.nonNull(audioListenHistory) && audioListenHistory.getContentType()>0){
            lastGenresId = audioListenHistory.getContentType().longValue();
        }
        List<AgeRangeDTO> ageRangeOptions = Lists.newArrayList();
        List<GenresOptionDTO> genresOptions = Lists.newArrayList();
        //通过AgeRangeEnum和GenresEnum构建 年龄段选项列表以及genres选项列表
        for (AudioListenEnum.AgeRange value : AudioListenEnum.AgeRange.values()) {
            AgeRangeDTO dto = new AgeRangeDTO();
            dto.setAgeRangeId(value.getCode().longValue());
            dto.setAgeRangeName(value.getDesc());
            dto.setIsChecked(dto.getAgeRangeId().equals(lastAgeRangeId));
            ageRangeOptions.add(dto);
        }
        for (AudioListenEnum.Genres value : AudioListenEnum.Genres.values()) {
            if (value.getCode().equals(AudioListenEnum.Genres.PODCAST.getCode()) && Objects.equals(SceneModeEnum.SLEEP_MODE.getValue(), sceneMode)) {
                continue;
            }
            GenresOptionDTO dto = new GenresOptionDTO();
            dto.setGenresId(value.getCode().longValue());
            dto.setGenresName(value.getDesc());
            dto.setIsChecked(dto.getGenresId().equals(lastGenresId));
            genresOptions.add(dto);
        }
        response.setAgeRangeOptions(ageRangeOptions);
        response.setGenresOptions(genresOptions);
        log.info("【获取playlist筛选项】AudioService.getAudioOptions param:{},{},result:{}", talId,sceneMode,JSONUtil.toJsonStr(response));
        return response;
    }


    public AudioListV2Response getAudioListV2(AudioListV2Request request) {
        AudioListV2Response response = new AudioListV2Response();
        //获取最终查询条件
        buildFinalQuery(request);
        //获取到播放列表
        List<AudioDTO> audioDTOList = obtainAudioList(request);
        //根据动作、最近一次听的音频，组装返回列表
        List<AudioDTO> audioDTOS = audioDTOListHandle(audioDTOList, request);
        //组装返回参数
        buildResult(request, response, audioDTOS);
        log.info("【获取播放列表】AudioService.obtainAudioList param:{},result:{}", JSONUtil.toJsonStr(request),JSONUtil.toJsonStr(response));
        return response;
    }

    private static void buildResult(AudioListV2Request request, AudioListV2Response response, List<AudioDTO> audioDTOS) {
        response.setAudioList(audioDTOS);
        response.setAudioId(request.getAudioId());
        response.setAgeRangeId(request.getAgeRangeId());
        response.setGenresId(request.getGenresId());
        response.setAlbumId(request.getAlbumId());
        response.setSceneMode(request.getSceneMode());
    }

    private List<AudioDTO> audioDTOListHandle(List<AudioDTO> audioDTOList, AudioListV2Request request) {
        if (CollUtil.isEmpty(audioDTOList)) {
            return Collections.emptyList();
        }
        int total = audioDTOList.size();
        int cursor = obtainCursor(request.getAudioId(), audioDTOList); // 获取起始位置索引
        int action = request.getAction();
        int size = request.getPageSize()>total?total:request.getPageSize();

        // 确保参数合法
        if (cursor < 0 || size <= 0) {
            return Collections.emptyList();
        }

        List<AudioDTO> result = new ArrayList<>(size);

        if (action == AudioListenEnum.Action.NEXT.getCode()) { // 正向
            for (int i = 0; i < size; i++) {
                result.add(audioDTOList.get((cursor + i) % total));
            }
        } else if (action == AudioListenEnum.Action.PRE.getCode()) { // 反向
            for (int i = 0; i < size; i++) {
                // (cursor - (i % total) + total) % total 避免负数，同时保持循环顺序
                int index = (cursor - (i % total) + total) % total;
                result.add(audioDTOList.get(index));
            }
        } else {
            throw new IllegalArgumentException("Unsupported action: " + action);
        }

        return result;
    }

    /**
     * 获取开始位置
     * @param audioId
     * @param audioDTOList
     * @return
     */
    private Integer obtainCursor(String audioId, List<AudioDTO> audioDTOList) {
        if(StrUtil.isBlank(audioId)){
            return 0;
        }
        List<String> audioIds = audioDTOList.stream().map(AudioDTO::getAudioId).toList();
        int cursor = audioIds.indexOf(audioId);
        return Math.max(cursor, 0);
    }

    private List<AudioDTO> obtainAudioList(AudioListV2Request request) {
        log.info("【获取播放列表】AudioService.obtainAudioList param:{}", JSONUtil.toJsonStr(request));
        //如果专辑id不为空，则直接查询专辑
        List<Audio> audios = null;
        if(nonNullAndGreaterZero(request.getAlbumId())){
            List<String> audioId = albumDetailMap.getOrDefault(request.getAlbumId(),Lists.newArrayList());
            audios = audioList.stream().filter(audio -> audioId.contains(audio.getUnionId())).toList();
        }else {
            audios = getAudioList(request);
        }
        if(CollUtil.isEmpty(audios)){
            return Lists.newArrayList();
        }
        //查询
        return audios.stream().map(audio -> {
            AudioDTO audioDTO = new AudioDTO();
            audioDTO.setAudioId(audio.getUnionId());
            audioDTO.setAudioName(audio.getName());
            audioDTO.setSceneMode(audio.getSceneMode());
            audioDTO.setGenresId(audio.getContentType().longValue());
            audioDTO.setSubjectDesc(audio.getSubjectDesc());
            audioDTO.setCoverUrl(cdnDomain+audio.getCoverUrl());
            audioDTO.setAudioUrl(cdnDomain+audio.getAudioUrl());
            audioDTO.setSequence(audio.getSequence());
            return audioDTO;
        }).toList();
    }

    private List<Audio> getAudioList(AudioListV2Request request) {
        return audioList.stream().filter(audio -> {
            if(Objects.isNull(request.getAgeRangeId()) || nonNullAndEqZero(request.getAgeRangeId())){
                return true;
            }else if (nonNullAndGeZero(request.getAgeRangeId())) {
                return audio.getAgeRangeIds().contains(request.getAgeRangeId().toString());
            }
            return true;
        }).filter(audio -> {
            if(Objects.isNull(request.getGenresId()) || nonNullAndEqZero(request.getGenresId())){
                return true;
            }else if (nonNullAndGeZero(request.getGenresId())) {
                return audio.getContentType() == request.getGenresId().intValue();
            }
            return true;
        }).filter(audio -> {
            if (Objects.nonNull(request.getSceneMode())) {
                return audio.getSceneMode() == request.getSceneMode().intValue();
            }
            return true;
        }).sorted(Comparator.comparing(Audio::getSequence)).toList();
    }

    /**
     * 不为空且等于0
     * @param value
     * @return
     */
    public Boolean nonNullAndEqZero(Long value) {
        return Objects.nonNull(value) && value == 0;
    }

    /**
     * 不为空且大于0
     * @param value
     * @return
     */
    public Boolean nonNullAndGreaterZero(Long value) {
        return Objects.nonNull(value) && value > 0;
    }

    /**
     * 不为空且大于等于0
     * @param value
     * @return
     */
    public Boolean nonNullAndGeZero(Long value) {
        return Objects.nonNull(value) && value >= 0;
    }

    /**
     * 构建最终查询条件
     * @param request
     */
    private void buildFinalQuery(AudioListV2Request request) {
        if(Objects.isNull(request.getAction())){
            //默认下一首
            request.setAction(1);
        }
        if(nonNullAndGreaterZero(request.getAlbumId())){
            return;
        }
        if(nonNullAndGeZero(request.getAgeRangeId()) || nonNullAndGeZero(request.getGenresId())){
            return;
        }
        //如果都为空需要查询最近一首音频的播放记录
        AudioListenHistory audioListenHistory = audioListenHistoryMapper.selectOne(Wrappers.<AudioListenHistory>lambdaQuery()
                        .eq(Objects.nonNull(request.getSceneMode()), AudioListenHistory::getSceneMode, request.getSceneMode())
                .eq(AudioListenHistory::getTalId, request.getTalId()).orderByDesc(AudioListenHistory::getListenTime).last("LIMIT 1"));
        if(Objects.isNull(audioListenHistory)){
            return;
        }
        if(nonNullAndGreaterZero(audioListenHistory.getAlbumId())){
            request.setAudioId(audioListenHistory.getAudioUnionId());
            request.setAlbumId(audioListenHistory.getAlbumId());
            request.setGenresId(audioListenHistory.getContentType().longValue());
            request.setAgeRangeId(audioListenHistory.getAgeRangeId());
            request.setSceneMode(request.getSceneMode());
            return;
        }
        AudioListenHistory lastHistory = audioListenHistoryMapper.selectOne(Wrappers.<AudioListenHistory>lambdaQuery()
                .eq(AudioListenHistory::getTalId, request.getTalId()).orderByDesc(AudioListenHistory::getListenTime).last("LIMIT 1"));
        if(nonNullAndGreaterZero(lastHistory.getAlbumId())){
            audioListenHistory = lastHistory;
        }
        request.setAudioId(audioListenHistory.getAudioUnionId());
        request.setAlbumId(audioListenHistory.getAlbumId());
        request.setGenresId(audioListenHistory.getContentType().longValue());
        request.setAgeRangeId(audioListenHistory.getAgeRangeId());
        request.setSceneMode(request.getSceneMode());
        return;
    }

    public Boolean reloadAudioList() {
        this.init();
        return Boolean.TRUE;
    }

}