package com.tal.sea.seaover.application.dto.request.schedules;

import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
public class ScheduleEditRequest implements Serializable {
    /**
     * 主键
     */
    @NotNull(message = "ID cannot be empty")
    private Long id;
    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId cannot be empty")
    private String unionId;

    /**
     * 日程名称
     */
    @NotBlank(message = "Name cannot be empty")
    private String name;

    /**
     * 日程时间（24小时制 由HH:MM 转为秒级时间）
     */
    @NotNull(message = "Schedule time cannot be empty")
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    @NotNull(message = "Reminder duration cannot be empty")
    private Integer notifyDuration;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "Repeat flag cannot be empty")
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性闹钟需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * label颜色
     */
    @NotBlank(message = "Color cannot be empty")
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    @NotNull(message = "Enabled flag cannot be empty")
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    @NotNull(message = "Preset flag cannot be empty")
    private Integer isPreSet;

    /**
     * 最后修改者，1-device，2-parent
     */
    @NotNull(message = "Last modifier cannot be empty")
    private Integer lastModifiedBy;

    public void checkParameter() {
        //一次性日程时 scheduleDay不能为空
        if (Objects.equals(repeating, YesNoEnum.NO.getValue())
                && StringUtils.isEmpty(scheduleDay)) {
            throw new BusinessException("ScheduleDay cannot be empty for one-time schedules");
        }
        if (Objects.equals(repeating, YesNoEnum.YES.getValue())
                && StringUtils.isEmpty(repeatDays)) {
            throw new BusinessException("RepeatDays cannot be empty for recurring schedules");
        }
        if (StringUtils.isEmpty(unionId)) {
            this.setUnionId(null);
        }
    }

}
