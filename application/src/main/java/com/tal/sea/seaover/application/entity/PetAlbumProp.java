package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 宠物相册道具实体
 */
@Data
@TableName(value = "tb_pet_album_prop")
public class PetAlbumProp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 宠物ID
     */
    private Integer petId;

    /**
     * 道具id编号
     */
    private String propId;

    /**
     * 解锁等级
     */
    private Integer level;

    /**
     * 缩略图资源
     */
    private String thumbnailUrl;

    /**
     * 大图资源
     */
    private String fullImageUrl;

    /**
     * 排列顺序
     */
    private Integer sort;

    /**
     * app version
     */
    private Long appVersion;

    /**
     * 逻辑删除字段
     */
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}