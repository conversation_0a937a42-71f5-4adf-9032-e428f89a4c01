package com.tal.sea.seaover.application.enums;


/**
 * <AUTHOR> 2025-05-08
 */
public enum YesNoEnum {

    NO(0, "否"), YES(1, "是");

    private int value;

    private String description;

    YesNoEnum(int _value, String _description) {
        this.value = _value;
        this.description = _description;
    }


    public int getValue() {
        return value;
    }


    public void setValue(int value) {
        this.value = value;
    }


    public String getDescription() {
        return description;
    }

   
    public void setDescription(String description) {
        this.description = description;
    }

    public static YesNoEnum valueOfType(int value) {
        for (YesNoEnum type : YesNoEnum.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }

    public static boolean contains(Integer value) {
        for (YesNoEnum c : YesNoEnum.values()) {
            if (c.value == value) {
                return true;
            }
        }
        return false;
    }
}
