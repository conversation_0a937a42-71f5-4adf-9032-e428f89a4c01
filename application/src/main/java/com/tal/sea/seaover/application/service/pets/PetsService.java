package com.tal.sea.seaover.application.service.pets;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.dto.request.pets.ListPetsRequest;
import com.tal.sea.seaover.application.dto.request.pets.VerifyNameRequest;
import com.tal.sea.seaover.application.dto.response.pets.PetsResponse;
import com.tal.sea.seaover.application.dto.response.pets.PetsSyncDataResponse;
import com.tal.sea.seaover.application.entity.Pets;
import com.tal.sea.seaover.application.mapper.PetsMapper;
import com.tal.sea.seaover.application.service.safety.ContentSafetyService;
import jakarta.validation.Valid;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
public class PetsService {
    @Autowired
    private PetsMapper petsMapper;
    @Autowired
    private ContentSafetyService contentSafetyService;

    public List<PetsResponse> list(@Valid ListPetsRequest request) {
        List<Pets> pets = ListPets(request.getTalId());
        if (pets == null) return null;
        return pets.stream().map(pet -> {
            PetsResponse petsResponse = new PetsResponse();
            BeanUtils.copyProperties(pet, petsResponse);
            return petsResponse;
        }).toList();
    }


    public List<Pets> ListPets(String talId) {
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getTalId, talId);
        return petsMapper.selectList(queryWrapper);
    }

    public boolean verifyName(VerifyNameRequest request) {
        return contentSafetyService.checkTextContentSafety(request.getPetName());
    }


    public List<PetsSyncDataResponse> listPetsSyncDataResponseByTalId(String talId) {
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getTalId, talId);
        List<Pets> pets = petsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(pets)) {
            return null;
        }
        return pets.stream().map(pet -> {
            PetsSyncDataResponse petsSyncDataResponse = new PetsSyncDataResponse();
            BeanUtils.copyProperties(pet, petsSyncDataResponse);
            return petsSyncDataResponse;
        }).toList();
    }


    public List<Pets> listByTalId(String talId) {
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getTalId, talId);
        return petsMapper.selectList(queryWrapper);
    }

    public boolean existsByUnionId(String unionId, String talId) {
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getUnionId, unionId);
        queryWrapper.eq(Pets::getTalId, talId);
        return petsMapper.selectCount(queryWrapper) > 0;
    }

    public List<Pets> listByUnionIds(String talId, Set<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return null;
        }
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Pets::getUnionId, unionIds);
        queryWrapper.eq(Pets::getTalId, talId);
        return petsMapper.selectList(queryWrapper);
    }

    public Pets getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return petsMapper.selectById(id);
    }


    public Pets getByTalIdAndPedId( String talId,Integer petId){
        if (Objects.isNull(talId)||Objects.isNull(petId)){
            return null;
        }
        LambdaQueryWrapper<Pets> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pets::getTalId, talId);
        queryWrapper.eq(Pets::getPetId, petId);
        return petsMapper.selectOne(queryWrapper);
    }

}
