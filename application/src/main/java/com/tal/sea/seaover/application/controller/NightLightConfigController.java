package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigEditForDeviceRequest;
import com.tal.sea.seaover.application.dto.request.nightLightConfig.NightLightConfigGetRequest;
import com.tal.sea.seaover.application.entity.NightLightConfig;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.nightLightConfig.NightLightConfigService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 夜灯配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/nightLightConfig")
public class NightLightConfigController {

    @Autowired
    private NightLightConfigService nightLightConfigService;
    @Resource
    private AlarmService alarmService;

    /**
     * 查询夜灯配置
     *
     * @param request 请求参数
     * @return 夜灯配置
     */
    @PostMapping("/get")
    public ResponseEntity getNightLightConfig(@RequestHeader("X-Tal-Id") String talId,@Valid @RequestBody NightLightConfigGetRequest request) {
        request.setTalId(talId);
        request.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
        NightLightConfig config = nightLightConfigService.getNightLightConfig(request);
        return ResponseUtil.successWithData(config);
    }

    /**
     * 编辑夜灯配置
     *
     * @param request 请求参数
     * @return 更新后的夜灯配置
     */
    @PostMapping("/edit")
    public ResponseEntity editNightLightConfig(@RequestHeader("X-Tal-Id") String talId,
                                               @Valid @RequestBody NightLightConfigEditForDeviceRequest request) {
        try {
            request.setTalId(talId);
            NightLightConfig config = nightLightConfigService.editNightLightConfig(request);
            return ResponseUtil.successWithData(config);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit alarms  fail! exception:{}", e.getMessage());
            alarmService.alarm("edit NightLightConfig fail! reason: " + e.getMessage());
            return ResponseUtil.failWith500("edit alarms fail!");
        }
    }

}
