package com.tal.sea.seaover.application.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DateUtils {

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 计算给定日期是星期几
     *
     * @param alarmDay 日期字符串，格式为 yyyy-MM-dd
     * @return 星期几的整数值（周一=1，周日=7）
     * @throws BusinessException 如果日期格式无效
     */
    public static String calculateDayOfWeek(String alarmDay) {
        try {
            LocalDate date = LocalDate.parse(alarmDay, FORMATTER);
            int dayOfWeek = date.getDayOfWeek().getValue(); // 周一=1，周日=7
            return String.valueOf(dayOfWeek);
        } catch (Exception e) {
            log.error("Failed to parse alarmDay: {}, error: {}", alarmDay, e.getMessage());
            throw new BusinessException(ErrorEnum.INVALID_ALARM_DAY_FORMAT);
        }
    }

    /**
     * 计算闹钟和日程的所属日期(如果未过当前时间今天否则是明天)
     *
     * @param alarmSeconds
     * @return
     */
    public static String calculateAlarmAndScheduleDate(int alarmSeconds) {
        LocalDateTime now = LocalDateTime.now();
        // 获取当前时间的秒数（从当天0点开始计算）
        int currentSeconds = now.getHour() * 3600 + now.getMinute() * 60 + now.getSecond();

        LocalDateTime alarmDateTime;
        if (alarmSeconds < currentSeconds) {
            // 如果闹钟时间大于当前时间s的秒数，则日期是明天
            alarmDateTime = now.plusDays(1);
        } else {
            // 否则日期是今天
            alarmDateTime = now;
        }
        // 返回格式化后的日期字符串
        return alarmDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 判断给定日期是否早于今天
     *
     * @param dateStr 日期字符串，格式为 yyyy-MM-dd
     * @return true 表示该日期已过，false 表示是今天或将来
     * @throws BusinessException 如果日期格式无效
     */
    public static boolean isPastDate(String dateStr) {
        try {
            LocalDate inputDate = LocalDate.parse(dateStr, FORMATTER);
            LocalDate today = LocalDate.now();
            return inputDate.isBefore(today);
        } catch (Exception e) {
            log.error("Failed to parse dateStr: {}, error: {}", dateStr, e.getMessage());
            throw new BusinessException(ErrorEnum.INVALID_ALARM_DAY_FORMAT);
        }
    }


    public static void main(String[] args) {
        System.out.println(isPastDate("2025-05-20"));
    }
}
