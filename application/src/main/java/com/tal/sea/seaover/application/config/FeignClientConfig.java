package com.tal.sea.seaover.application.config;

import feign.Feign;
import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class FeignClientConfig {
    @Bean
    public Request.Options optionsForSpecificEndpoint() {
        return new Request.Options(10L, TimeUnit.SECONDS,
                3L, TimeUnit.SECONDS, true);
    }

    @Bean
    public Feign.Builder feignBuilderForSpecificEndpoint() {
        return Feign.builder()
                .options(optionsForSpecificEndpoint());
    }
}
