package com.tal.sea.seaover.application.config.filter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.util.Enumeration;
import java.util.Map;
import java.util.Vector;

/**
 * Created by shu<PERSON><PERSON>n .
 * 将请求数据流填充到request的parameter集合中
 * 可以在这里对参数值增删改查操作
 * 不过，如果再尝试getInputStream时将获取不到数据
 */
public class RepeatParameterRequestWrapper extends HttpServletRequestWrapper {

    private Map<String, String[]> parameterMap;

    public RepeatParameterRequestWrapper(HttpServletRequest request) {
        super(request);
        parameterMap = request.getParameterMap();
    }

    @Override
    public Enumeration<String> getParameterNames() {
        Vector<String> vector = new Vector<String>(parameterMap.keySet());
        return vector.elements();
    }

    @Override
    public String getParameter(String name) {
        String[] results = parameterMap.get(name);
        return results == null ? null : results[0];
    }

    @Override
    public String[] getParameterValues(String name) {
        return parameterMap.get(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return parameterMap;
    }

    public void setParameterMap(Map<String, String[]> parameterMap) {
        this.parameterMap = parameterMap;
    }
}
