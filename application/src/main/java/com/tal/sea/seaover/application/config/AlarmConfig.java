package com.tal.sea.seaover.application.config;

import com.xesv5.dog.Alarm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class AlarmConfig {
    @Value("${sea.alarm.xiaotianquantaskid}")
    private Integer xiaotianquanTaskid;
    @Value("${sea.alarm.xiaotianquantoken}")
    private String xiaotianquanToken;


    @Bean("xiaotianquanAlarm")
    public Alarm xiaotianquanAlarm() {
        Alarm alarm = new Alarm.Builder(xiaotianquanTaskid, xiaotianquanToken)
                .setBaseUri("https://alarm.tal.com")
                .build();

        return alarm;
    }

}
