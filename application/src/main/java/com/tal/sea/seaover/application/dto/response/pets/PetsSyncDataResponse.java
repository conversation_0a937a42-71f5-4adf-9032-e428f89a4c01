package com.tal.sea.seaover.application.dto.response.pets;

import com.tal.sea.seaover.application.dto.response.syncdata.SyncDataParentResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 宠物
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PetsSyncDataResponse extends SyncDataParentResponse implements Serializable {

    /**
     * 宠物ID
     */
    private Integer petId;

    /**
     * 宠物名称
     */
    private String petName;

    /**
     * 宠物等级，1-4
     */
    private Integer petLevel;

    /**
     * 是否选中，0-未选中，1-选中
     */
    private Integer selected;

    /**
     * 饥饿度
     */
    private BigDecimal hunger;

    /**
     * 不开心度
     */
    private BigDecimal unhappy;

    /**
     * 不洁净
     */
    private BigDecimal dirt;

    /**
     * 交互值
     */
    private BigDecimal rp;

    /**
     * 起床闹钟
     */
    private Integer wakeupAlarmCount;

    /**
     * 睡觉闹钟
     */
    private Integer sleepAlarmCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}
