package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户夜灯设置
 *
 * @TableName tb_night_light_config
 */
@TableName(value = "tb_night_light_config")
@Data
public class NightLightConfig implements Serializable {
    /**w
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * union_id
     */
    private String unionId;

    /**
     * 此数据对应的设备端中的ID值
     */
    private Integer clientId;

    /**
     * 亮度
     */
    private Integer brightness;

    /**
     * 灯效 1常亮 2闪烁效果 3呼吸效果
     */
    private Integer lightingEffects;

    /**
     * 自动照明，0-关闭，1-开启
     */
    private Integer autoLight;

    /**
     * 灯光颜色
     */
    private String color;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}