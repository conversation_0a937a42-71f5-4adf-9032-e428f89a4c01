package com.tal.sea.seaover.application.enums;

import lombok.Getter;

@Getter
public enum DataSyncProcessorEnum {

    /**
     * 闹钟
     */
    ALARM(1, "alarms"),
    /**
     * 日程
     */
    SCHEDULE(2, "schedules"),
    /**
     * 夜灯
     */
    NIGHT_LIGHT(3, "nightLights"),
    /**
     * 宠物
     */
    PETS(4, "pets");


    private int value;
    private String type;

    DataSyncProcessorEnum(int key, String msg) {
        this.value = key;
        this.type = msg;
    }


    public static DataSyncProcessorEnum fromType(Integer type) {
        for (DataSyncProcessorEnum value : values()) {
            if (value.getValue() == type) {
                return value;
            }
        }
        return null;
    }

}
