package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.request.schedules.*;
import com.tal.sea.seaover.application.dto.response.schedule.UserScheduleResponse;
import com.tal.sea.seaover.application.entity.UserSchedule;
import com.tal.sea.seaover.application.enums.ErrorEnum;
import com.tal.sea.seaover.application.enums.IdentityEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.userschedule.UserScheduleService;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 给设备端提供的日程接口
 */
@Slf4j
@RestController
@RequestMapping("/api/datahub/cube/schedule")
public class UserScheduleController {
    @Autowired
    private UserScheduleService userScheduleService;
    @Resource
    private AlarmService alarmService;

    /**
     * 新增用户日程
     */
    @PostMapping("/add")
    public ResponseEntity addSchedule(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated ScheduleAddForDeviceRequest scheduleAddRequest) {
        try {
            ScheduleAddRequest request = new ScheduleAddRequest();
            BeanUtils.copyProperties(scheduleAddRequest, request);
            request.setTalId(talId);
            request.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            UserScheduleResponse scheduleResponse = userScheduleService.addSchedule(request);
            return ResponseUtil.successWithData(scheduleResponse);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("add schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("add schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.ADD_SCHEDULE_FAIL.getMsg());
        }
    }

    /**
     * 获取用户日程
     */
    @PostMapping("/get")
    public ResponseEntity<UserScheduleResponse> getSchedule(@RequestBody @Validated GetScheduleRequest getScheduleRequest) {
        UserScheduleResponse scheduleResponse = userScheduleService.getScheduleResponse(getScheduleRequest.getScheduleId());
        return ResponseUtil.successWithData(scheduleResponse);
    }


    /**
     * 设置日程状态
     */
    @PostMapping("/setEnabled")
    public ResponseEntity setEnabled(@RequestBody @Validated SetScheduleEnabledForDeviceRequest setScheduleEnabledForDeviceRequest) {
        try {
            SetScheduleEnabledRequest setScheduleEnabledRequest = new SetScheduleEnabledRequest();
            BeanUtils.copyProperties(setScheduleEnabledForDeviceRequest, setScheduleEnabledRequest);
            setScheduleEnabledRequest.setScheduleId(setScheduleEnabledForDeviceRequest.getId());
            setScheduleEnabledRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            UserSchedule result = userScheduleService.setEnabled(setScheduleEnabledRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("set schedule enabled fail! exception:{}", e.getMessage());
            alarmService.alarm("set schedule enabled fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.SET_SCHEDULE_ENABLED_FAIL.getMsg());
        }
    }

    /**
     * 编辑日程
     */
    @PostMapping("/edit")
    public ResponseEntity edit(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated ScheduleEditForDeviceRequest scheduleEditForDeviceRequest) {
        try {
            ScheduleEditRequest scheduleEditRequest = new ScheduleEditRequest();
            BeanUtils.copyProperties(scheduleEditForDeviceRequest, scheduleEditRequest);
            scheduleEditRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            scheduleEditRequest.setTalId(talId);
            UserSchedule result = userScheduleService.edit(scheduleEditRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("edit schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("edit schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500("edit schedule fail!");
        }
    }

    /**
     * 删除用户日程
     */
    @PostMapping("/delete")
    public ResponseEntity deleteSchedule(@RequestHeader("X-Tal-Id") String talId, @RequestBody @Validated DeleteScheduleForDeviceRequest deleteScheduleForDeviceRequest) {
        try {
            DeleteScheduleRequest deleteScheduleRequest = new DeleteScheduleRequest();
            deleteScheduleRequest.setTalId(talId);
            deleteScheduleRequest.setScheduleId(deleteScheduleForDeviceRequest.getId());
            deleteScheduleRequest.setLastModifiedBy(IdentityEnum.DEVICE.getValue());
            deleteScheduleRequest.setUnionId(deleteScheduleForDeviceRequest.getUnionId());
            UserSchedule result = userScheduleService.deleteSchedule(deleteScheduleRequest);
            return ResponseUtil.successWithData(result);
        } catch (BusinessException e) {
            return ResponseUtil.responseWithoutData(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("delete schedule fail! exception:{}", e.getMessage());
            alarmService.alarm("delete schedule fail! reason:" + e.getMessage());
            return ResponseUtil.failWith500(ErrorEnum.DELETE_SCHEDULE_FAIL.getMsg());
        }
    }
}